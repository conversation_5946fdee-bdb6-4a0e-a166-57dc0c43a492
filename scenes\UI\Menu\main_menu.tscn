[gd_scene load_steps=122 format=4 uid="uid://csojs3on01j0j"]

[ext_resource type="Script" uid="uid://p6nyte5s4ul6" path="res://scripts/UI/menu_pages/main_menu.gd" id="1_qw34p"]
[ext_resource type="Shader" uid="uid://yj6bs51fsu4t" path="res://shaders/sky.gdshader" id="2_oskeh"]
[ext_resource type="PackedScene" uid="uid://crnq1makno6d1" path="res://assets/Kenny/block-grass-overhang-large.glb" id="3_di7yh"]
[ext_resource type="Texture2D" uid="uid://bymde2j4vycbh" path="res://assets/Kenny/Textures/colormap.png" id="4_usqtc"]
[ext_resource type="PackedScene" uid="uid://cxc3n0k7aovh8" path="res://scenes/NewPlayer/model/model.tscn" id="6_agrmb"]
[ext_resource type="Texture2D" uid="uid://b8lu6rth286kb" path="res://assets/icons/menu_buttons/ranking_button.png" id="6_vkox4"]
[ext_resource type="FontFile" uid="uid://b14qykl1gecp1" path="res://assets/icons/ranking/Light.ttf" id="7_14q37"]
[ext_resource type="Texture2D" uid="uid://bl31qpd5tt7n2" path="res://assets/icons/menu_buttons/friends_button.png" id="7_85ot6"]
[ext_resource type="Script" uid="uid://cr2twkljoyot8" path="res://scripts/UI/responsive_ui.gd" id="7_1605n"]
[ext_resource type="Texture2D" uid="uid://cgykw33lg3nma" path="res://assets/icons/menu_buttons/chat_button.png" id="8_xy7t6"]
[ext_resource type="Texture2D" uid="uid://b2se4nlos2rth" path="res://assets/icons/menu_buttons/group_button.png" id="9_y7vn1"]
[ext_resource type="Texture2D" uid="uid://c364jyy2rkmbn" path="res://assets/icons/menu_buttons/challenge.png" id="10_p77mf"]
[ext_resource type="Texture2D" uid="uid://jyhqhy2ion1" path="res://assets/icons/unseen.png" id="11_oiqfe"]
[ext_resource type="Texture2D" uid="uid://cw4bv011xptm8" path="res://assets/icons/menu_buttons/shop.png" id="12_jno4w"]
[ext_resource type="Shader" uid="uid://bgt1w5dofdqng" path="res://shaders/profile.gdshader" id="13_pdyfn"]
[ext_resource type="Texture2D" uid="uid://n1n713kck7jx" path="res://assets/icons/menu_buttons/mail.png" id="16_bqxaw"]
[ext_resource type="Texture2D" uid="uid://bd42b8m7n3mdx" path="res://assets/images/profile_placeHolder.png" id="16_g0t05"]
[ext_resource type="Texture2D" uid="uid://bucerrgio4ybc" path="res://assets/icons/menu_buttons/settings.png" id="17_ahcr5"]
[ext_resource type="Script" uid="uid://onrnu1syb5n1" path="res://scripts/netImage.gd" id="17_bkn6e"]
[ext_resource type="Texture2D" uid="uid://dl6v8d2125xs0" path="res://assets/icons/menu_buttons/name_button2.svg" id="20_o7jfq"]
[ext_resource type="Texture2D" uid="uid://dxi35ofr6gis5" path="res://assets/icons/menu_buttons/Button (Orange).svg" id="20_o7wn2"]
[ext_resource type="Texture2D" uid="uid://m5ycj14lakga" path="res://assets/icons/menu_buttons/Button (Aqua).svg" id="21_7f01q"]
[ext_resource type="FontFile" uid="uid://cdh63neq4ginm" path="res://addons/toastparty/fonts/Light.ttf" id="21_a5u1h"]
[ext_resource type="Texture2D" uid="uid://c8jl7lqhgac4i" path="res://assets/new/coin_menu.png" id="21_bkn6e"]
[ext_resource type="Texture2D" uid="uid://cy5dhhrt6643v" path="res://assets/new/gem_menu.png" id="22_bkn6e"]
[ext_resource type="Texture2D" uid="uid://dakvwcdctrspd" path="res://assets/new/cup_menu2.png" id="23_agrmb"]
[ext_resource type="Texture2D" uid="uid://dckdyxwukw2t6" path="res://assets/new/ChatGPT Image Jul 23, 2025, 10_56_15 AM 1.png" id="27_oiqfe"]
[ext_resource type="Texture2D" uid="uid://d25afaefn84lh" path="res://assets/images/bombtag_icon.png" id="28_eurag"]
[ext_resource type="Shader" uid="uid://c43vgrxjpfqb0" path="res://blur.gdshader" id="28_o7jfq"]
[ext_resource type="PackedScene" uid="uid://cleac0011v0c0" path="res://scenes/UI/Ranking/rank_card.tscn" id="30_a5u1h"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_eurag"]
sky_top_color = Color(0.228725, 0.486073, 0.75, 1)
sky_horizon_color = Color(0.543182, 0.630007, 0.6708, 1)
sky_curve = 0.0861524
ground_bottom_color = Color(0.227451, 0.486275, 0.74902, 1)
ground_horizon_color = Color(0.545098, 0.631373, 0.670588, 1)
ground_curve = 0.0207053

[sub_resource type="Sky" id="Sky_o7jfq"]
sky_material = SubResource("ProceduralSkyMaterial_eurag")

[sub_resource type="Environment" id="Environment_o7jfq"]
background_mode = 2
background_energy_multiplier = 1.5
sky = SubResource("Sky_o7jfq")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
ambient_light_energy = 1.3

[sub_resource type="ShaderMaterial" id="ShaderMaterial_a5u1h"]
render_priority = 0
shader = ExtResource("2_oskeh")
shader_parameter/curve_amount = 0.0
shader_parameter/cloud_density = 0.512
shader_parameter/cloud_speed = Vector2(0.05, 0)
shader_parameter/scale_x = 20.0
shader_parameter/scale_y = 25.0
shader_parameter/softness = 0.087

[sub_resource type="SphereMesh" id="SphereMesh_b7qyv"]
flip_faces = true
radius = 20.0
height = 15.0
is_hemisphere = true

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_oiqfe"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_eurag"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1.5, 1.25, 1.25, 1.25)]
op_type = 2
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_o7jfq"]
constant = 0.9

[sub_resource type="Gradient" id="Gradient_oiqfe"]
offsets = PackedFloat32Array(0.186131, 1)
colors = PackedColorArray(0.246094, 0.246094, 0.246094, 1, 1, 1, 1, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_1ev52"]
noise_type = 2
seed = 6
frequency = 0.12
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_tkl2m"]
seamless = true
color_ramp = SubResource("Gradient_oiqfe")
noise = SubResource("FastNoiseLite_1ev52")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_a5u1h"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_b7qyv"]
output_port_for_preview = 0
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0.0744019, 0.532943, 0.999944, 1)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_ct8ng"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_3fosh"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_p6vq2"]
default_input_values = [1, Vector2(0.01, 0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_hkjuw"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_5tr6a"]
default_input_values = [1, Vector2(-0.01, -0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_nvdau"]
input_name = "time"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_nmkb7"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_vine8"]
default_input_values = [1, Vector2(0.05, 0.025), 2, Vector2(0, 0)]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_utg4i"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_47dmq"]
seamless = true
noise = SubResource("FastNoiseLite_utg4i")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_ptdqp"]
texture = SubResource("NoiseTexture2D_47dmq")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_62icn"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_shtbd"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_upbca"]
default_input_values = [0, Vector4(0, 0, 0, 0), 1, Vector4(1, 1, 1, 1), 2, Vector4(0, 0, 0, 0)]
op_type = 3

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_68dkm"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(5, 0.5, 0.5)]
operator = 2

[sub_resource type="VisualShader" id="VisualShader_03tfp"]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_4;
uniform vec4 ColorParameter : source_color = vec4(0.074402, 0.532943, 0.999944, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_15;



void vertex() {
// Input:2
	float n_out2p0 = TIME;


// UVFunc:3
	vec2 n_in3p1 = vec2(0.05000, 0.02500);
	vec2 n_out3p0 = vec2(n_out2p0) * n_in3p1 + UV;


// Texture2D:4
	vec4 n_out4p0 = texture(tex_vtx_4, n_out3p0);


// Input:6
	vec3 n_out6p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(5.00000, 0.50000, 0.50000);
	vec3 n_out8p0 = n_out6p0 * n_in8p1;


// Input:5
	vec3 n_out5p0 = VERTEX;


// MultiplyAdd:7
	vec4 n_out7p0 = (n_out4p0 * vec4(n_out8p0, 0.0)) + vec4(n_out5p0, 0.0);


// Output:0
	VERTEX = vec3(n_out7p0.xyz);


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.01000, 0.01000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:9
	float n_out9p0 = TIME;


// UVFunc:8
	vec2 n_in8p1 = vec2(-0.01000, -0.01000);
	vec2 n_out8p0 = vec2(n_out9p0) * n_in8p1 + UV;


// Texture2D:15
	vec4 n_out15p0 = texture(tex_frg_15, n_out8p0);


// VectorOp:10
	vec4 n_out10p0 = n_out3p0 * n_out15p0;


// VectorOp:11
	vec4 n_in11p1 = vec4(1.50000, 1.25000, 1.25000, 1.25000);
	vec4 n_out11p0 = pow(n_out10p0, n_in11p1);


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 + n_out11p0;


// FloatConstant:13
	float n_out13p0 = 0.900000;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ROUGHNESS = n_out13p0;


}
"
nodes/vertex/0/position = Vector2(640, 120)
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_nmkb7")
nodes/vertex/2/position = Vector2(-552.787, -31.8079)
nodes/vertex/3/node = SubResource("VisualShaderNodeUVFunc_vine8")
nodes/vertex/3/position = Vector2(-132.787, -31.8079)
nodes/vertex/4/node = SubResource("VisualShaderNodeTexture_ptdqp")
nodes/vertex/4/position = Vector2(147.213, -11.8079)
nodes/vertex/5/node = SubResource("VisualShaderNodeInput_62icn")
nodes/vertex/5/position = Vector2(-552.787, 488.192)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_shtbd")
nodes/vertex/6/position = Vector2(-532.787, 228.192)
nodes/vertex/7/node = SubResource("VisualShaderNodeMultiplyAdd_upbca")
nodes/vertex/7/position = Vector2(387.213, 408.192)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_68dkm")
nodes/vertex/8/position = Vector2(-172.787, 348.192)
nodes/vertex/connections = PackedInt32Array(2, 0, 3, 2, 3, 0, 4, 0, 4, 0, 7, 0, 5, 0, 7, 2, 6, 0, 8, 0, 8, 0, 7, 1, 7, 0, 0, 0)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_b7qyv")
nodes/fragment/2/position = Vector2(-720, 40)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_ct8ng")
nodes/fragment/3/position = Vector2(-1320, 0)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_3fosh")
nodes/fragment/4/position = Vector2(-160, 180)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_p6vq2")
nodes/fragment/5/position = Vector2(-1720, 0)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_hkjuw")
nodes/fragment/6/position = Vector2(-2200, 80)
nodes/fragment/8/node = SubResource("VisualShaderNodeUVFunc_5tr6a")
nodes/fragment/8/position = Vector2(-1720, 400)
nodes/fragment/9/node = SubResource("VisualShaderNodeInput_nvdau")
nodes/fragment/9/position = Vector2(-2200, 480)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_oiqfe")
nodes/fragment/10/position = Vector2(-980, 280)
nodes/fragment/11/node = SubResource("VisualShaderNodeVectorOp_eurag")
nodes/fragment/11/position = Vector2(-700, 440)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_o7jfq")
nodes/fragment/13/position = Vector2(-160, 660)
nodes/fragment/15/node = SubResource("VisualShaderNodeTexture_a5u1h")
nodes/fragment/15/position = Vector2(-1360, 360)
nodes/fragment/connections = PackedInt32Array(5, 0, 3, 0, 6, 0, 5, 2, 9, 0, 8, 2, 3, 0, 10, 0, 10, 0, 11, 0, 2, 0, 4, 0, 11, 0, 4, 1, 4, 0, 0, 0, 13, 0, 0, 3, 8, 0, 15, 0, 15, 0, 10, 1)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_anrm4"]
render_priority = 0
shader = SubResource("VisualShader_03tfp")
shader_parameter/ColorParameter = Color(0.0484937, 0.336469, 0.533333, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_2cv8x"]
material = SubResource("ShaderMaterial_anrm4")
size = Vector2(100, 100)
subdivide_width = 32
subdivide_depth = 32

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2voq8"]
resource_name = "colormap"
cull_mode = 2
albedo_color = Color(0.601223, 0.601223, 0.601223, 1)
albedo_texture = ExtResource("4_usqtc")
texture_filter = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_eurag"]
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"format": 34896613377,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAEwARABwAHAAdABMAFwAVAB4AHgAfABcAIgAgACEAIQAjACIAGQAgACIAIgAbABkAFAAWABAAEAASABQAIQAfAB4AHgAjACEADAAdABwAHAANAAwADgAPABgAGAAaAA4AHgAVAAsACwAKAB4AHgAKAAgACAAjAB4ABgAiACMAIwAIAAYAGwAiAAYABgAEABsAAgAMAA4ADgAAAAIABQATAB0AHQADAAUABwASABMAEwAFAAcAAwAdAAwADAACAAMACQAUABIAEgAHAAkAFQAUAAkACQALABUAAQAaABsAGwAEAAEAAQAAAA4ADgAaAAEAJQAkAA4AJgAlAA4ADgAnACYAKQAoACcAKgApACcAJwArACoAKwAMACwALAAtACsALQAuACsAGgAOAC8ALwAwABoAMwAxADIAMgA0ADMANAA1ADMANQAbADYANwA1ADYANgA4ADcAOQAiADQAOQA0ADoAOgA7ADkAGgAwADwAPAAbABoAPQAeACMAIwA+AD0APwA+ACMAIwAiAD8AQQAVAEAAQABCAEEAQABDAEIARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkASwAUABUAFQBMAEsATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUADAAdAFgAWABZAAwAWgBYAB0AHQATAFoAFABLAFsAWwASABQAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAZwBlAGgAaABpAGcAZABqAGsAawBdAGQAXQBlAGQAXQBcAGUAXABoAGUAXABsAGgAbABtAGgAbABgAG0AYABuAG0AYABiAG4AbwBsAFwAXABeAG8AaQBoAG0AbQBwAGkAZgBxAGoAagBkAGYAbgBiAGMAYwByAG4AbQBuAHIAcgBwAG0AcQBzAGsAawBqAHEAYQBgAGwAbABvAGEAcwBfAF0AXQBrAHMA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4AHAANAAwADgAPABgADAATABwAEwARABwAWABZAAwADAATAFgAWgBYABMAKwAMACwALAAtACsALQAuACsAEQATABIAEgAQABEABwASABMAEgAWABAAJwArACoAKgApACcAKQAoACcAFQASAAcAFgASABUAFQAXABYASwASABUAEgBLAFsAFQBMAEsAFwAVAB4AHgAfABcAQQAVAEAAQABCAEEAQABDAEIAIQAfAB4ARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkAHgAiACEAIgAgACEAPQAeACIAIgA+AD0APwA+ACIAGQAgACIAIgAbABkAGwAYABkAOQAiADQAOQA0ADoAOgA7ADkAMgA0ADMAMwAxADIANAA1ADMANQAbADYANwA1ADYANgA4ADcAGwAwADwALwAwABsAGwAOAC8ADgAbAAEAGAAbAA4AJQAkAA4AJgAlAA4ADgAnACYAHgAVAAcABwAiAB4AGwAiAAcAGwAHAAEADgABAAcABwAMAA4ABwATAAwATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUAbABfAF4AbABeAG8AbABvAGEAYQBjAGwAYwByAGwAcgBwAGwAcwBfAGwAcQBzAGwAbABwAGkAbABpAGcAbABnAGYAZgBxAGwA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 116,
"vertex_data": PackedByteArray("8vr//0rZAAAW8f//FvEAAPL6//+0JgAAFvH//+gOAABK2f//8voAAErZ//8MBQAAtCb///L6AAC0Jv//DAUAAOgO//8W8QAA6A7//+gOAAAMBf//StkAAAwF//+0JgAA////31cjAADy+v+/tCYAAP///9+n3AAA8vr/v0rZAAC0Jv+/DAUAAErZ/78MBQAAVyP/3wAAAACn3P/fAAAAAFkK/99ZCgAAAAD/31cjAADoDv+/6A4AAAwF/7+0JgAAFvH/vxbxAABK2f+/8voAAKX1/9+l9QAAp9z/3///AAAW8f+/6A4AAKX1/99ZCgAAAAD/36fcAAAMBf+/StkAALQm/7/y+gAA6A7/vxbxAABXI//f//8AAFkK/9+l9QAA//+wrrbUAAD//6yZxb0AAP//sK7TpgAA////3+KeAAD//7Cu8ZYAAP//rJn/fwAA//+wrg1pAAD////fHGEAAP//sK5IKwAA//+smTlCAAD//7CuK1kAADL9Ha5q4wAApfWsmaX1AAD/f6yZ//8AAA1psK7//wAA8Zawrv//AAAcYf/f//8AAOKe/9///wAAttSwrv//AADTprCu//8AAMW9rJn//wAASCuwrv//AAArWbCu//8AADlCrJn//wAAauMdrjL9AADMAh2uauMAAFkKrJml9QAAlBwdrjL9AAAAAP/fHGEAAAAAsK5IKwAAAACsmTlCAAAAALCuK1kAAAAA/9/ingAAAACwrg1pAAAAAKyZ/38AAAAAsK7xlgAAAACwrtOmAAAAAKyZxb0AAAAAsK621AAAWQqsmVkKAADMAh2ulBwAAP9/rJkAAAAA8ZawrgAAAAANabCuAAAAAOKe/98AAAAAHGH/3wAAAABIK7CuAAAAACtZsK4AAAAAOUKsmQAAAAC21LCuAAAAANOmsK4AAAAAxb2smQAAAACl9ayZWQoAADL9Ha6UHAAAauMdrswCAACUHB2uzAIAAErZAAAMBQAAFvEAAOgOAABK2f+/DAUAABbx/7/oDgAA6A4AAOgOAADoDv+/6A4AAAwFAAC0JgAADAX/v7QmAAAW8QAAFvEAAErZAADy+gAAFvH/vxbxAABK2f+/8voAALQmAADy+gAAtCb/v/L6AADy+gAAStkAAPL6AAC0JgAAtCYAAAwFAADoDgAAFvEAAAwFAABK2QAAtCb/vwwFAADoDv+/FvEAAPL6/79K2QAADAX/v0rZAADy+v+/tCYAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_dtoc6"]
resource_name = "block-grass-overhang-large_block-grass-overhang-large"
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"attribute_data": PackedByteArray("/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zf/n/83mLn/N/+f/zeYuf83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N/+f/zeYuf83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N5i5/zf/n/83/5//N5i5/zf/n/83mLn/N5i5/zeYuf83/5//N/+f/zf/n/83ZYb/N/+f/zdlhv83ZYb/N2WG/zf/n/83/5//N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83/5//N2WG/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N2WG/zf/n/83ZYb/N/+f/zfysf83/5//N5i5/zfysf83/5//N/Kx/zeYuf838rH/N/+f/zf/n/838rH/N5i5/zfysf83/5//Nyey/zf/n/83mLn/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zeYuf83J7L/N/+f/zf/n/83/5//N/+f/zcnsv83mLn/N5i5/zf/n/83J7L/N/+f/zf/n/83/5//N/Kx/zeYuf838rH/N/+f/zfysf83mLn/N/Kx/zf/n/838rH/N5i5/zfysf83/5//N/+f/zeYuf83J7L/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zf/n/83mLn/N/+f/zcnsv83mLn/N/+f/zcnsv83/5//N5i5/zcnsv83/5//N/+f/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3mPn/t2XG/7dlxv+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t2XG/7dlxv+3mPn/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t5j5/7dlxv+3mPn/t2XG/7eY+f+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t5j5/7dlxv+3mPn/t2XG/zf/n/83/5//N/+f/zf/n/83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N/+f/zf/n/83ZYb/N2WG/zcnsv83J7L/t5j5/7eY+f+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3mPn/t5j5/7eY+f+3mPk="),
"format": 34896613399,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbwBuAG0AbQBwAG8AcgBxAHAAcwByAHAAcAB0AHMAdAB1AHYAdgB3AHQAdwB4AHQAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AgACBAH8AgQCCAIMAhACBAIMAgwCFAIQAhwCGAIAAhwCAAIgAiACJAIcAjACKAIsAiwCNAIwAkACOAI8AjwCRAJAAlACSAJMAkwCVAJQAmACWAJcAlwCZAJgAlwCaAJkAnACXAJsAmwCdAJwAmwCeAJ0AoACbAJ8AnwChAKAAnwCiAKEApQCjAKQApACmAKUAqQCnAKgAqACqAKkAqgCrAKkAqwCsAK0ArgCrAK0ArQCvAK4AsQCwAKoAsQCqALIAsgCzALEAtgC0ALUAtQC3ALYAugC4ALkAuQC7ALoAvgC8AL0AvQC/AL4AwgDAAMEAwQDDAMIAxgDEAMUAxQDHAMYAygDIAMkAyQDLAMoAzgDMAM0AzQDPAM4A0gDQANEA0QDTANIA0wDUANIA0wDVANQA1QDWANQA1QDXANYA1wDYANYA1wDZANgA2QDaANgA2QDbANoA3gDcAN0A3QDfAN4A4gDgAOEA4QDjAOIA5gDkAOUA5QDnAOYA6gDoAOkA6QDrAOoA7gDsAO0A7QDvAO4A8gDwAPEA8QDzAPIA9gD0APUA9QD3APYA+gD4APkA+QD7APoA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4ABQENAAwADgAPAAIBDAATAAUBEwARAAUBtQC3AAwADAATALUAugC1ABMAdAAMAHYAdgB3AHQAdwB4AHQAEQATABIAEgAQABEAVgASABMAEgABARAAcAB0AHMAcwByAHAAcgBxAHAA/wASAFYAAQESAP8A/wAXAAEBpQASAP8AEgClAL0A/wCmAKUAFwD/ACEAIQAjABcAmAD/AJcAlwCZAJgAlwCaAJkAJQAjACEAnACXAJsAmwCdAJwAmwCeAJ0AoACbACEAIQChAKAAIQCiAKEAIQAHASUABwEkACUADAEhAAcBBwGRAAwBlACRAAcBGQAkAAcBBwEDARkAAwECARkAhwAHAYAAhwCAAIgAiACJAIcAfgCAAH8AfwB9AH4AgACBAH8AgQADAYMAhACBAIMAgwCFAIQAAwF8AIsACwF8AAMBAwEOAAsBDgADAWYAAgEDAQ4AbgBsAA4AbwBuAA4ADgBwAG8ABgEAAQkBCQEIAQYBBAEIAQkBBAEJAQoB/QAKAQkBCQH8AP0ACQH+APwAqQCnAKgAqACqAKkAqgCrAKkAqwASAK0ArgCrAK0ArQCvAK4AsQATAKoAsQCqALIAsgCzALEA3AANAcIA3ADCAN4A3ADeAMUAxQDHANwAxwDrABIB6wAUARIBGAENAREBFgEXAREBEQETARABEQEQAQ8BEQEPAQ4BDgEVAREB")],
"material": SubResource("StandardMaterial3D_2voq8"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 281,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_eurag")

[sub_resource type="BoxShape3D" id="BoxShape3D_p7nmn"]
size = Vector3(2.04535, 0.953339, 1.5011)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5hyct"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_snwii"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_8l2my"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_fwete"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2dst6"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_61rq3"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_e4u3s"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ek6bb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ct8ng"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3fosh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_p6vq2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_hkjuw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sv0a7"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_f6g2w"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_yosjy"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ehf5a"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_mgp3n"]
shader = ExtResource("13_pdyfn")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gfw7m"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_pip1o"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_mh2gn"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ym4bp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_botwy"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_lrk64"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_rud4e"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_cnfqg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_51gpo"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_h1o5e"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_4kofa"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_afhde"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_levub"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_jmx0n"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ewxb2"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_dsre8"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6v2r0"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ek4mb"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_pigx7"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_m4ej8"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3h88m"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_dy287"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_bgbf7"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_q6l7r"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_w6a4u"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_6e213"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_0e26e"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_wimu2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_agrmb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_eurag"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_o7jfq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_a5u1h"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_b7qyv"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_b7qyv"]
shader = ExtResource("28_o7jfq")
shader_parameter/lod = 0.0
shader_parameter/tint_color = Color(1, 1, 1, 0)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_o7jfq"]
shader = ExtResource("28_o7jfq")
shader_parameter/lod = 2.302
shader_parameter/tint_color = Color(0.0260925, 0.0359318, 0.0390625, 1)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5tr6a"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nvdau"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nmkb7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vine8"]

[node name="MainMenu" type="Node3D"]
script = ExtResource("1_qw34p")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_o7jfq")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.887202, -0.423769, 0.182463, -0.0922675, 0.224523, 0.970091, -0.452062, -0.877502, 0.160097, 0, 0, 0)
light_energy = 2.25
light_specular = 2.5

[node name="Cloud" type="MeshInstance3D" parent="."]
transform = Transform3D(50, 0, 0, 0, 50, 0, 0, 0, 50, 3.39157, -53.3244, -0.590649)
material_override = SubResource("ShaderMaterial_a5u1h")
cast_shadow = 0
mesh = SubResource("SphereMesh_b7qyv")
skeleton = NodePath("../..")

[node name="Ocean" type="Node3D" parent="."]
transform = Transform3D(7.674, 0, 0, 0, 7.674, 0, 0, 0, 7.674, 0, 0, -187.752)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Ocean"]
mesh = SubResource("PlaneMesh_2cv8x")

[node name="block-grass-overhang-large" parent="." instance=ExtResource("3_di7yh")]
transform = Transform3D(4.09576, 0, 2.86787, 0, 5, 0, -2.86787, 0, 4.09576, 3, -1.58475, 0)

[node name="block-grass-overhang-large" parent="block-grass-overhang-large" index="0"]
mesh = SubResource("ArrayMesh_dtoc6")

[node name="CollisionShape3D" type="CollisionShape3D" parent="block-grass-overhang-large"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00357056, 0.520737, 0.250549)
shape = SubResource("BoxShape3D_p7nmn")

[node name="Model" parent="block-grass-overhang-large" instance=ExtResource("6_agrmb")]
unique_name_in_owner = true
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, -0.34044, 1, 0.365091)

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.939693, 0.34202, 0, -0.34202, 0.939693, -0.2, 9.3944, 14.1283)
fov = 70.0

[node name="GUI" type="CanvasLayer" parent="."]
script = ExtResource("7_1605n")

[node name="Menu" type="Control" parent="GUI"]
unique_name_in_owner = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Ranking" type="Button" parent="GUI/Menu"]
layout_mode = 2
offset_left = 128.0
offset_top = 623.0
offset_right = 527.0
offset_bottom = 1021.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_5hyct")
theme_override_styles/hover = SubResource("StyleBoxTexture_snwii")
theme_override_styles/pressed = SubResource("StyleBoxTexture_8l2my")
theme_override_styles/normal = SubResource("StyleBoxTexture_fwete")
icon = ExtResource("6_vkox4")

[node name="Label" type="Label" parent="GUI/Menu/Ranking"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -113.0
offset_top = -37.506
offset_right = 113.0
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_colors/font_outline_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Ranking"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Friends" type="Button" parent="GUI/Menu"]
layout_mode = 2
offset_left = 343.5
offset_top = 408.0
offset_right = 742.5
offset_bottom = 806.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_2dst6")
theme_override_styles/hover = SubResource("StyleBoxTexture_61rq3")
theme_override_styles/pressed = SubResource("StyleBoxTexture_e4u3s")
theme_override_styles/normal = SubResource("StyleBoxTexture_ek6bb")
icon = ExtResource("7_85ot6")

[node name="Label" type="Label" parent="GUI/Menu/Friends"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -113.0
offset_top = -37.506
offset_right = 113.0
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_colors/font_outline_color = Color(0.00218187, 0.322917, 0.484375, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Friends"
horizontal_alignment = 1

[node name="unseen_messages" type="Button" parent="GUI/Menu/Friends"]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -74.0
offset_top = -30.0
offset_right = 70.0
offset_bottom = 114.0
grow_horizontal = 0
scale = Vector2(0.6, 0.6)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 72
theme_override_styles/focus = SubResource("StyleBoxEmpty_ct8ng")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3fosh")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_p6vq2")
theme_override_styles/normal = SubResource("StyleBoxEmpty_hkjuw")
text = "0"
icon = ExtResource("11_oiqfe")
icon_alignment = 1

[node name="Chat" type="Button" parent="GUI/Menu"]
layout_mode = 2
offset_left = 559.0
offset_top = 408.0
offset_right = 958.0
offset_bottom = 806.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_2dst6")
theme_override_styles/hover = SubResource("StyleBoxTexture_61rq3")
theme_override_styles/pressed = SubResource("StyleBoxTexture_e4u3s")
theme_override_styles/normal = SubResource("StyleBoxTexture_ek6bb")
icon = ExtResource("8_xy7t6")

[node name="Label" type="Label" parent="GUI/Menu/Chat"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -113.0
offset_top = -37.506
offset_right = 113.0
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_colors/font_outline_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Chat"
horizontal_alignment = 1

[node name="Button4" type="Button" parent="GUI/Menu"]
modulate = Color(0.772549, 0.772549, 0.772549, 0.745098)
layout_mode = 2
offset_left = 344.0
offset_top = 623.0
offset_right = 743.0
offset_bottom = 1021.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_2dst6")
theme_override_styles/hover = SubResource("StyleBoxTexture_61rq3")
theme_override_styles/pressed = SubResource("StyleBoxTexture_e4u3s")
theme_override_styles/normal = SubResource("StyleBoxTexture_ek6bb")
icon = ExtResource("9_y7vn1")

[node name="Label" type="Label" parent="GUI/Menu/Button4"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -113.0
offset_top = -37.506
offset_right = 113.0
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_colors/font_outline_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Group"
horizontal_alignment = 1

[node name="Button5" type="Button" parent="GUI/Menu"]
modulate = Color(0.772549, 0.772549, 0.772549, 0.745098)
layout_mode = 2
offset_left = 559.5
offset_top = 623.0
offset_right = 958.5
offset_bottom = 1021.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_2dst6")
theme_override_styles/hover = SubResource("StyleBoxTexture_61rq3")
theme_override_styles/pressed = SubResource("StyleBoxTexture_e4u3s")
theme_override_styles/normal = SubResource("StyleBoxTexture_ek6bb")
icon = ExtResource("10_p77mf")

[node name="Label" type="Label" parent="GUI/Menu/Button5"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -113.0
offset_top = -37.506
offset_right = 113.0
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_colors/font_outline_color = Color(0.00392157, 0.321569, 0.486275, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Challenges"
horizontal_alignment = 1

[node name="Shop" type="Button" parent="GUI/Menu"]
layout_mode = 1
offset_left = 120.0
offset_top = 401.0
offset_right = 547.0
offset_bottom = 821.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_sv0a7")
theme_override_styles/hover = SubResource("StyleBoxTexture_f6g2w")
theme_override_styles/pressed = SubResource("StyleBoxTexture_yosjy")
theme_override_styles/normal = SubResource("StyleBoxTexture_ehf5a")
icon = ExtResource("12_jno4w")

[node name="Label" type="Label" parent="GUI/Menu/Shop"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.647
anchor_right = 0.5
anchor_bottom = 0.647
offset_left = -111.833
offset_top = -37.506
offset_right = 114.167
offset_bottom = 37.494
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0.435294, 0.384314, 0, 1)
theme_override_colors/font_outline_color = Color(0.433594, 0.383349, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Shop"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextureRect" type="TextureRect" parent="GUI/Menu"]
material = SubResource("ShaderMaterial_mgp3n")
layout_mode = 0
offset_left = 150.0
offset_top = 41.0
offset_right = 1174.0
offset_bottom = 1065.0
scale = Vector2(0.14, 0.14)
texture = ExtResource("16_g0t05")
script = ExtResource("17_bkn6e")
pic_name = "223.jpg"

[node name="Profile name" type="Button" parent="GUI/Menu"]
layout_mode = 0
offset_left = 128.0
offset_top = 40.0
offset_right = 316.0
offset_bottom = 219.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_gfw7m")
theme_override_styles/hover = SubResource("StyleBoxTexture_pip1o")
theme_override_styles/pressed = SubResource("StyleBoxTexture_mh2gn")
theme_override_styles/normal = SubResource("StyleBoxTexture_ym4bp")
icon = ExtResource("20_o7jfq")
vertical_icon_alignment = 2

[node name="Label" type="Label" parent="GUI/Menu/Profile name"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -79.0
offset_top = -53.0
offset_right = 79.0
offset_bottom = -24.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_outline_color = Color(0.191406, 0.191406, 0.191406, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("21_a5u1h")
theme_override_font_sizes/font_size = 24
text = "Nima Belak"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Mail" type="Button" parent="GUI/Menu"]
layout_mode = 0
offset_left = 344.0
offset_top = 46.0
offset_right = 456.0
offset_bottom = 145.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_botwy")
theme_override_styles/hover = SubResource("StyleBoxTexture_lrk64")
theme_override_styles/pressed = SubResource("StyleBoxTexture_rud4e")
theme_override_styles/normal = SubResource("StyleBoxTexture_cnfqg")
icon = ExtResource("16_bqxaw")

[node name="Settings" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -174.0
offset_top = 33.0
offset_right = -66.0
offset_bottom = 141.0
grow_horizontal = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_51gpo")
theme_override_styles/hover = SubResource("StyleBoxTexture_h1o5e")
theme_override_styles/pressed = SubResource("StyleBoxTexture_4kofa")
theme_override_styles/normal = SubResource("StyleBoxTexture_afhde")
icon = ExtResource("17_ahcr5")

[node name="Coin" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -460.0
offset_top = 43.0
offset_right = 73.0
offset_bottom = 236.0
grow_horizontal = 0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_levub")
theme_override_styles/hover = SubResource("StyleBoxTexture_jmx0n")
theme_override_styles/pressed = SubResource("StyleBoxTexture_ewxb2")
theme_override_styles/normal = SubResource("StyleBoxTexture_dsre8")
icon = ExtResource("21_bkn6e")

[node name="Label" type="Label" parent="GUI/Menu/Coin"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -72.5
offset_top = -27.5
offset_right = 113.5
offset_bottom = 28.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 48
text = "123234"
horizontal_alignment = 1

[node name="Cup" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -742.0
offset_top = 36.0
offset_right = -206.0
offset_bottom = 259.0
grow_horizontal = 0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_6v2r0")
theme_override_styles/hover = SubResource("StyleBoxTexture_ek4mb")
theme_override_styles/pressed = SubResource("StyleBoxTexture_pigx7")
theme_override_styles/normal = SubResource("StyleBoxTexture_m4ej8")
icon = ExtResource("22_bkn6e")

[node name="Label" type="Label" parent="GUI/Menu/Cup"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -27.5
offset_right = 106.0
offset_bottom = 28.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 48
text = "212"
horizontal_alignment = 1

[node name="Cup2" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -982.0
offset_top = 40.0
offset_right = -538.0
offset_bottom = 249.0
grow_horizontal = 0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_6v2r0")
theme_override_styles/hover = SubResource("StyleBoxTexture_ek4mb")
theme_override_styles/pressed = SubResource("StyleBoxTexture_pigx7")
theme_override_styles/normal = SubResource("StyleBoxTexture_m4ej8")
icon = ExtResource("23_agrmb")

[node name="Label" type="Label" parent="GUI/Menu/Cup2"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -256.417
offset_top = -28.0
offset_right = -70.417
offset_bottom = 28.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 48
text = "212"
horizontal_alignment = 1

[node name="City" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -464.0
offset_top = -181.0
offset_right = 357.0
offset_bottom = 157.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(0.5, 0.417)
theme_override_styles/focus = SubResource("StyleBoxEmpty_3h88m")
theme_override_styles/hover = SubResource("StyleBoxTexture_dy287")
theme_override_styles/pressed = SubResource("StyleBoxTexture_bgbf7")
theme_override_styles/normal = SubResource("StyleBoxTexture_q6l7r")
icon = ExtResource("20_o7wn2")

[node name="Label" type="Label" parent="GUI/Menu/City"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.461
anchor_right = 0.5
anchor_bottom = 0.461
offset_left = -331.5
offset_top = -68.818
offset_right = 330.5
offset_bottom = 69.182
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0.46875, 0.181588, 0.00211149, 1)
theme_override_constants/shadow_offset_y = 6
theme_override_constants/outline_size = 30
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 120
text = "City"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Games" type="Button" parent="GUI/Menu"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -464.0
offset_top = -345.0
offset_right = 357.0
offset_bottom = -7.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(0.5, 0.417)
theme_override_styles/focus = SubResource("StyleBoxTexture_w6a4u")
theme_override_styles/hover = SubResource("StyleBoxTexture_6e213")
theme_override_styles/pressed = SubResource("StyleBoxTexture_0e26e")
theme_override_styles/normal = SubResource("StyleBoxTexture_wimu2")
icon = ExtResource("21_7f01q")

[node name="Label" type="Label" parent="GUI/Menu/Games"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.461
anchor_right = 0.5
anchor_bottom = 0.461
offset_left = -331.5
offset_top = -68.818
offset_right = 330.5
offset_bottom = 69.182
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0.345098, 0.235294, 1)
theme_override_constants/shadow_offset_y = 6
theme_override_constants/outline_size = 30
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 120
text = "Games"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Search" type="Control" parent="GUI"]
unique_name_in_owner = true
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="GUI/Search"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.033
anchor_top = 0.296
anchor_right = 0.471
anchor_bottom = 0.975
offset_left = 0.639999
offset_top = 0.255997
offset_right = -0.320007
offset_bottom = -1.40002
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_agrmb")

[node name="PlayerJoinContainer" type="GridContainer" parent="GUI/Search/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 840.0
offset_bottom = 584.0
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 62
theme_override_constants/v_separation = 40
columns = 7

[node name="Label" type="Label" parent="GUI/Search"]
layout_mode = 0
offset_left = 64.0
offset_top = 56.0
offset_right = 725.0
offset_bottom = 131.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Searching for players . . ."
vertical_alignment = 1

[node name="players_count" type="Label" parent="GUI/Search"]
layout_mode = 0
offset_left = 744.0
offset_top = 56.0
offset_right = 896.0
offset_bottom = 131.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "0/10"
vertical_alignment = 1

[node name="Label2" type="Label" parent="GUI/Search"]
layout_mode = 0
offset_left = 64.0
offset_top = 152.0
offset_right = 399.0
offset_bottom = 194.0
theme_override_colors/font_color = Color(0.223529, 0.682353, 0.878431, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 36
text = "Estimated Wait Time : "
vertical_alignment = 1

[node name="wait_time" type="Label" parent="GUI/Search"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 408.0
offset_top = 152.0
offset_right = 464.0
offset_bottom = 194.0
theme_override_colors/font_color = Color(0.223529, 0.682353, 0.878431, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 36
text = "20"
horizontal_alignment = 1
vertical_alignment = 1

[node name="back" type="Button" parent="GUI/Search"]
z_index = 7
layout_mode = 0
offset_left = 1728.0
offset_top = 32.0
offset_right = 1856.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_eurag")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o7jfq")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_a5u1h")
theme_override_styles/normal = SubResource("StyleBoxEmpty_b7qyv")
icon = ExtResource("27_oiqfe")

[node name="GamesMenu" type="Control" parent="GUI"]
unique_name_in_owner = true
visible = false
material = SubResource("ShaderMaterial_b7qyv")
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameMenuPanel" type="Panel" parent="GUI/GamesMenu"]
material = SubResource("ShaderMaterial_o7jfq")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="dark_panel" type="Panel" parent="GUI/GamesMenu"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -8.0
offset_bottom = 8.0
grow_horizontal = 2
grow_vertical = 2

[node name="GemPacks" type="ScrollContainer" parent="GUI/GamesMenu"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_top = -280.0
offset_right = 1920.0
offset_bottom = 312.0
grow_vertical = 2
horizontal_scroll_mode = 3
vertical_scroll_mode = 3

[node name="HBoxContainer" type="HBoxContainer" parent="GUI/GamesMenu/GemPacks"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 20
alignment = 1

[node name="ShopItem" type="Control" parent="GUI/GamesMenu/GemPacks/HBoxContainer"]
custom_minimum_size = Vector2(594, 442)
layout_mode = 2
mouse_filter = 1

[node name="TextureRect" type="TextureRect" parent="GUI/GamesMenu/GemPacks/HBoxContainer/ShopItem"]
custom_minimum_size = Vector2(594, 442)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -297.0
offset_top = -221.0
offset_right = 297.0
offset_bottom = 221.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("28_eurag")

[node name="bomb_tag" type="Button" parent="GUI/GamesMenu/GemPacks/HBoxContainer/ShopItem/TextureRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5tr6a")
theme_override_styles/hover = SubResource("StyleBoxEmpty_nvdau")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_nmkb7")
theme_override_styles/normal = SubResource("StyleBoxEmpty_vine8")

[node name="game_menu_back" type="Button" parent="GUI/GamesMenu"]
z_index = 7
layout_mode = 0
offset_left = 48.0
offset_top = 32.0
offset_right = 176.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_eurag")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o7jfq")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_a5u1h")
theme_override_styles/normal = SubResource("StyleBoxEmpty_b7qyv")
icon = ExtResource("27_oiqfe")

[node name="BrowseServer" type="Control" parent="GUI"]
unique_name_in_owner = true
visible = false
material = SubResource("ShaderMaterial_b7qyv")
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameMenuPanel" type="Panel" parent="GUI/BrowseServer"]
material = SubResource("ShaderMaterial_o7jfq")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="dark_panel" type="Panel" parent="GUI/BrowseServer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -8.0
offset_bottom = 8.0
grow_horizontal = 2
grow_vertical = 2

[node name="browse_menu_back" type="Button" parent="GUI/BrowseServer"]
z_index = 7
layout_mode = 0
offset_left = 48.0
offset_top = 32.0
offset_right = 176.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_eurag")
theme_override_styles/hover = SubResource("StyleBoxEmpty_o7jfq")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_a5u1h")
theme_override_styles/normal = SubResource("StyleBoxEmpty_b7qyv")
icon = ExtResource("27_oiqfe")

[node name="Label" type="Label" parent="GUI/BrowseServer"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -198.0
offset_top = -376.0
offset_right = 198.0
offset_bottom = -301.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.384314, 0.47451, 1)
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("7_14q37")
theme_override_font_sizes/font_size = 64
text = "Browse Server"

[node name="ScrollContainer" type="ScrollContainer" parent="GUI/BrowseServer"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.067
anchor_top = 0.231
anchor_right = 0.933
anchor_bottom = 0.963
offset_left = -0.639999
offset_top = -7.584
offset_right = 0.639893
offset_bottom = -0.0320435
vertical_scroll_mode = 3

[node name="RankList" type="VBoxContainer" parent="GUI/BrowseServer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 16

[node name="RankCard" parent="GUI/BrowseServer/ScrollContainer/RankList" instance=ExtResource("30_a5u1h")]
layout_mode = 2

[node name="CountDownTimer" type="Timer" parent="."]
unique_name_in_owner = true

[connection signal="pressed" from="GUI/Menu/Ranking" to="." method="_on_ranking_pressed"]
[connection signal="pressed" from="GUI/Menu/Friends" to="." method="_on_friends_pressed"]
[connection signal="pressed" from="GUI/Menu/Chat" to="." method="_on_chat_pressed"]
[connection signal="pressed" from="GUI/Menu/Shop" to="." method="_on_shop_pressed"]
[connection signal="pressed" from="GUI/Menu/Profile name" to="." method="_on_profile_name_pressed"]
[connection signal="pressed" from="GUI/Menu/Settings" to="." method="_on_settings_pressed"]
[connection signal="pressed" from="GUI/Menu/City" to="." method="_on_city_pressed"]
[connection signal="pressed" from="GUI/Menu/Games" to="." method="_on_games_pressed"]
[connection signal="pressed" from="GUI/Search/back" to="." method="_on_back_pressed"]
[connection signal="pressed" from="GUI/GamesMenu/GemPacks/HBoxContainer/ShopItem/TextureRect/bomb_tag" to="." method="_on_bomb_tag_pressed"]
[connection signal="pressed" from="GUI/GamesMenu/game_menu_back" to="." method="_on_game_menu_back_pressed"]
[connection signal="pressed" from="GUI/BrowseServer/browse_menu_back" to="." method="_on_browse_menu_back_pressed"]
[connection signal="timeout" from="CountDownTimer" to="." method="_on_count_down_timer_timeout"]

[editable path="block-grass-overhang-large"]
