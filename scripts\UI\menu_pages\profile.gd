extends Node3D

@onready var camera_3d: Camera3D = $Camera3D
@onready var menu: Control = $GUI/Profile
@onready var platform: StaticBody3D = $"block-grass-overhang-large"
@onready var edit_character: Sprite3D = $<PERSON><PERSON><PERSON>cter
@onready var choose_character_menu: Control = $GUI/<PERSON>ose<PERSON>haracter
@onready var back_button: Button = $GUI/back



@onready var copy_user_name: Button = $GUI/Profile/TextureRect2/CopyUserName
@onready var copy_check: Button = $GUI/Profile/TextureRect2/Copy_Check

@export var change_username_hud : PackedScene
@export var change_bio_hud : PackedScene
@export var change_profile_hud : PackedScene


var target_position: Vector3 = Vector3(-12, 10, 16.5)
var target_rotation: float = 0.0
var is_moving: bool = false
var speed: float = 4.0
var rotation_speed: float = 4.0

enum MENU_STATE{PROFILE , CHOOSE_CHARACTER, SKIN_SELECTION}
var current_menu_state: MENU_STATE = MENU_STATE.PROFILE



# Reference to the profile picture node
@onready var profile_picture: TextureRect = $GUI/Profile/ProfilePicture
@onready var user_name: Label = $GUI/Profile/TextureRect2/UserName
@onready var bio: Label = $GUI/Profile/Bio/Bio
@onready var kol: Label = $GUI/Profile/Panel2/VBoxContainer/kol
@onready var win: Label = $GUI/Profile/Panel2/VBoxContainer/win
@onready var age: Label = $GUI/Profile/Panel2/VBoxContainer/age
@onready var coin: Label = $GUI/Profile/TextureRect2/Coin_img/coin
@onready var cup: Label = $GUI/Profile/TextureRect2/Cup_img/cup
@onready var price_label: Label = %price_label
var skin_price := 0


@onready var category_grid: GridContainer = %CategoryGrid
@onready var item_grid: GridContainer = %ItemGrid
@onready var skin_coin_icon: TextureRect = $GUI/skin_coin
@onready var skin_coin_value: Label = $GUI/skin_coin/skin_coin_value



const SKIN_CARD_SCENE = preload("res://scenes/UI/Profile/skin_card.tscn")
const SKIN_CARD_CLEAR_SCENE = preload("res://skin_card_clear.tscn")
const ITEM_BASE_PATH = "user://assets/skins"

var message_hud = preload("res://scenes/UI/HUD/message_hud.tscn")
signal set_model_skin
@onready var set_skin_button: Button = $GUI/SetSkin
var selected_item_prices: Dictionary = {}
const CATEGORY_TO_INDEX = {
	"shirts": 0, "pants": 1, "shoes": 2, "hair": 3,
	"hat": 4, "gloves": 5, "glasses": 6, "emotion": 7, "mustache": 8
}

var skin_parameter_holder : Array = [0,0,0,0,0,0,0,0,0]

func _ready() -> void:
	skin_parameter_holder = Global.skin_parameters
	#cache_skin_resources()
	set_player_skin()
	for category_name in CATEGORY_TO_INDEX.keys():
		selected_item_prices[category_name] = 0
	
	# Set the initial button text to "0"
	#set_skin_button.text = "0"
	choose_character_menu.visible = false
	copy_check.visible = false
	Global.profile = self
	
	#item_grid.hide()
	
	# Connect the 'category_selected' signal from each child in the category grid.
	for category_card in category_grid.get_children():
		if category_card.has_signal("category_selected"):
			category_card.category_selected.connect(_on_category_selected)


	age.text = "Account Age: " + str(Global.player_age)
	coin.text = str(Global.player_coin)
	cup.text = str(Global.player_cup)
	user_name.text = Global.global_player_name
	kol.text = "Games Played: " + str(Global.player_kol)
	win.text = "Games Won: " + str(Global.player_win)
	bio.text = Global.player_bio
	skin_coin_value.text = str(Global.player_coin)
	#show_change_name_panel
	Tcpserver.connect("update_profile", Callable(self, "refresh_profile_data"))
	Tcpserver.connect("show_change_name_panel", Callable(self, "show_change_name"))
	Tcpserver.connect("show_change_bio_panel", Callable(self, "show_change_bio"))
	Tcpserver.connect("show_change_pic_panel", Callable(self, "show_change_pic"))
	Tcpserver.connect("buyskin", Callable(self, "_on_buyskin"))

	# Load the initial profile image
	#load_initial_profile_image("res://assets/images/profile_placeHolder.png")

	# Set the initial texture for the profile picture
	if Global.current_profile_image:
		profile_picture.texture = Global.current_profile_image

	# Connect to the global signal for profile image updates
	Global.connect("profile_image_updated", _on_profile_image_updated)


func set_player_skin():
	var skin_meshes := %Model.get_node("Skeleton_01/Skeleton3D/").get_children()

	# Remove last 2 skins if unwanted
	skin_meshes = skin_meshes.slice(0, skin_meshes.size() - 2)

	for i in range(min(skin_meshes.size(), Global.skin_parameters.size())):
		var skin = skin_meshes[i]
		var param_index = Global.skin_parameters[i]

		var mesh_child = %Model.get_node("Skeleton_01/Skeleton3D/" + str(skin.name))

		if param_index == 0:
			# Clear mesh if index is 0
			mesh_child.mesh = null
			#print("Cleared mesh for: ", skin.name)
		else:
			# Build path to file
			var resource_path = "user://scenes/NewPlayer/resources/".path_join(str(skin.name))
			var file_path = resource_path.path_join(str(param_index) + ".tres")

			var resource = load(file_path)
			if resource is Mesh:
				mesh_child.mesh = resource
				#print("Applied: ", file_path)
			else:
				push_warning("Could not load mesh: %s" % file_path)

func cache_skin_resources():
	var list_file = FileAccess.open("res://asset_list.json", FileAccess.READ)
	if not list_file:
		push_error("asset_list.json not found!")
		return
	
	var json_data = JSON.parse_string(list_file.get_as_text())
	if not json_data:
		push_error("Failed to parse asset_list.json.")
		return

	print("Starting resource caching process using ResourceSaver...")
	for res_path in json_data:
		if not res_path.is_absolute_path():
			push_error("Path in JSON is not absolute: %s" % res_path)
			continue

		var cache_path = res_path.replace("res://", "user://")
		
		if not FileAccess.file_exists(cache_path):
			# Ensure the destination directory exists.
			var cache_dir = cache_path.get_base_dir()
			DirAccess.make_dir_recursive_absolute(cache_dir)

			# 1. Load the resource data into memory, just like with the image.
			var resource_to_cache: Resource = load(res_path)
			
			if resource_to_cache:
				# 2. Use ResourceSaver to write the in-memory resource to a new file.
				var error = ResourceSaver.save(resource_to_cache, cache_path)
				if error == OK:
					print("Cached: %s" % cache_path)
				else:
					push_error("ResourceSaver failed to save %s. Error: %s" % [res_path, error])
			else:
				push_error("Failed to load resource for caching: %s" % res_path)
			
	print("Caching complete.")

func refresh_profile_data() -> void:
	print("SIGNAL EMITTED!!!")
	age.text = "Account Age: " + str(Global.player_age)
	coin.text = str(Global.player_coin)
	skin_coin_value.text = str(Global.player_coin)
	cup.text = str(Global.player_cup)
	user_name.text = Global.global_player_name
	kol.text = "Games Played: " + str(Global.player_kol)
	win.text = "Games Won: " + str(Global.player_win)
	bio.text = Global.player_bio


func load_initial_profile_image(image_path: String) -> void:
	# Create a new Image object
	var image = Image.new()

	# Load the image from the specified path
	var error = image.load(image_path)
	if error != OK:
		print("Failed to load image: ", image_path)
		return

	# Convert the Image to an ImageTexture
	var image_texture = ImageTexture.create_from_image(image)

	# Set the global variable to the loaded image texture
	Global.current_profile_image = image_texture

	# Optionally, print a message to confirm the image was loaded
	print("Initial profile image loaded and set to Global.current_profile_image.")

func _on_profile_image_updated() -> void:
	# Update the profile picture texture when the global image changes
	if Global.current_profile_image:
		profile_picture.texture = Global.current_profile_image
		print("Profile picture updated with new image.")

func _process(delta: float) -> void:
	if is_moving:
		camera_3d.position = camera_3d.position.lerp(target_position, speed * delta)

		var current_rotation = platform.rotation_degrees.y
		var new_rotation = lerp(current_rotation, target_rotation, rotation_speed * delta)
		platform.rotation_degrees.y = new_rotation
		
		if camera_3d.position.distance_to(target_position) < 0.005 and abs(new_rotation - target_rotation) < 0.1:
			camera_3d.position = target_position
			platform.rotation_degrees.y = target_rotation
			is_moving = false

func _on_area_3d_input_event(camera: Node, event: InputEvent, event_position: Vector3, normal: Vector3, shape_idx: int) -> void:
	if event is InputEventMouseButton:
		if event.pressed:
			target_position = Vector3(12, 9.7188, 16.5)
			target_rotation = 80.0
			is_moving = true
			edit_character.visible = false
			choose_character_menu.visible = true
			menu.visible = false
			current_menu_state = MENU_STATE.CHOOSE_CHARACTER
			
			category_grid.show()
			#skin_coin_value.text = str(Global.player_coin)
			skin_coin_icon.show()
			#set_skin_button.show()
			#item_grid.hide()

func _on_back_pressed() -> void:
	#print(current_menu_state)
	
	match current_menu_state:
		MENU_STATE.SKIN_SELECTION:
			%ItemScrollContainer.hide()
			%CategoryScrollContainer.show()
			#set_skin_button.show()
			current_menu_state = MENU_STATE.CHOOSE_CHARACTER
			# Clear items from the grid to free memory
			for child in item_grid.get_children():
				child.queue_free()
		MENU_STATE.CHOOSE_CHARACTER:
			%ItemScrollContainer.hide()
			%CategoryScrollContainer.show()
			skin_coin_icon.hide()
			set_skin_button.hide()
			target_position = Vector3(-12, 9.7188, 16.5)
			target_rotation = 40.0
			is_moving = true
			edit_character.visible = true
			choose_character_menu.visible = false
			menu.visible = true
			current_menu_state = MENU_STATE.PROFILE
		MENU_STATE.PROFILE:
			#LoadScene.change_level("res://scenes/UI/Menu/main_menu.tscn")
			print("First: " + str(Global.skin_parameters))
			get_tree().change_scene_to_file("res://scenes/UI/Menu/main_menu.tscn")
			
			
func _on_category_selected(category_name: String) -> void:
	print(category_name)
	Global.skin_category = category_name
	var loaded_meshes: Array[Mesh] = []

	# 3. Construct the full path to the category folder
	var resource_path = "user://scenes/NewPlayer/resources/".path_join(category_name)
	print("RES PATH : ",resource_path )
	# 4. Use DirAccess to open and read the directory
	var dir = DirAccess.open(resource_path)
	if dir:
		# 5. Iterate over every file in the directory
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			# 6. Check if the entry is a file and ends with ".tres"
			if not dir.current_is_dir() and file_name.ends_with(".tres"):
				var file_path = resource_path.path_join(file_name)
				var resource = load(file_path)

				# 7. (Recommended) Verify the loaded resource is a Mesh
				if resource is Mesh:
					loaded_meshes.append(resource)
				else:
					push_warning("Resource at path %s is not a Mesh." % file_path)
			
			file_name = dir.get_next() # Move to the next file
	else:
		push_error("Could not open directory: %s" % resource_path)

	
	print("Successfully loaded %d meshes from %s" % [loaded_meshes.size(), category_name])
	%CategoryScrollContainer.hide()
	%ItemScrollContainer.show()
	#set_skin_button.show()
	var sb = %ItemScrollContainer.get_v_scroll_bar()
	sb.ratio = 0
	current_menu_state = MENU_STATE.SKIN_SELECTION
	populate_items(category_name)

func _on_item_selected(item_index: String , item_price: String) -> void:
	# --- This part of your code is correct ---
	var category_name = Global.skin_category
	var item_resource_path = "user://scenes/NewPlayer/resources/".path_join(category_name).path_join(item_index + ".tres")

	var loaded_mesh: Mesh = load(item_resource_path)

	if loaded_mesh:
		var mesh_child = %Model.get_node("Skeleton_01/Skeleton3D/" + category_name)
		if mesh_child:
			mesh_child.mesh = loaded_mesh
			skin_parameter_holder[mesh_child.get_index()] = int(item_index)
		else:
			push_error("Node not found for category: " + category_name)
	else:
		push_error("Failed to load mesh resource: " + item_resource_path)

	# --- CORRECTED PRICE LOGIC STARTS HERE ---
	selected_item_prices[category_name] = int(item_price)

	# 2. Recalculate the total price from scratch.
	var total_skin_price = 0
	for price in selected_item_prices.values():
		total_skin_price += price

	# 3. Update the button text with the new total.
	#set_skin_button.text = str(total_skin_price)

	print("Item selected in category '%s'. New total price: %d" % [category_name, total_skin_price])
	if total_skin_price != 0:
		set_skin_button.show()
	else:
		set_skin_button.hide()
	price_label.text = str(total_skin_price)
	skin_price = total_skin_price
	#set_model_skin.emit()

func _on_clear_skin() -> void:
	# Get the current category name
	var category_name = Global.skin_category

	# Get the mesh child for this category
	var mesh_child = %Model.get_node("Skeleton_01/Skeleton3D/" + category_name)
	if mesh_child:
		# Clear the mesh by setting it to null
		mesh_child.mesh = null
		# Set the skin parameter to 0 (cleared)
		skin_parameter_holder[mesh_child.get_index()] = 0
		print("Cleared skin for category: " + category_name)
	else:
		push_error("Node not found for category: " + category_name)

	# Set the price for this category to 0 since clearing is free
	selected_item_prices[category_name] = 0

	# Recalculate the total price
	var total_skin_price = 0
	for price in selected_item_prices.values():
		total_skin_price += price

	# Update the UI
	if total_skin_price != 0:
		set_skin_button.show()
	else:
		set_skin_button.show()
	price_label.text = str(total_skin_price)
	skin_price = total_skin_price

func _on_skin_accepted():
	%SkinChange.play()
	set_skin_button.hide()
	Global.skin_parameters = skin_parameter_holder

	# Build the server string where each parameter is exactly 2 digits
	var set_skin_server_param := ""
	for p in Global.skin_parameters:
		var n := int(p) % 100        # keep last two digits just in case
		var s := str(n)
		if s.length() < 2:
			s = "0" + s
		set_skin_server_param += s
	
	
	# Send skin data to server
	Tcpserver.send_skin_data(set_skin_server_param,skin_price)
	

	set_model_skin.emit()
	%ItemScrollContainer.hide()
	%CategoryScrollContainer.show()
	current_menu_state = MENU_STATE.CHOOSE_CHARACTER

	# Clear items from the grid to free memory
	for child in item_grid.get_children():
		child.queue_free()
		
	for item in selected_item_prices:
		selected_item_prices[item] = 0
		
func _on_buyskin(status:String):
	if status == "1":
		Tcpserver.send_request("gudp")
	else:
		ToastParty.show({
		"text": "    Not enough coins    ",
		"bgcolor": Color(1.0, 0.163, 0.193, 0.435),
		"color": Color(1, 1, 1, 1),
		"gravity": "top",
		"direction": "center",
		"text_size": 24,
		"use_font": true
	})



# This function reads a directory and creates a skin_card for each image.
func populate_items(folder_name: String) -> void:
	# Clear previous items
	for child in item_grid.get_children():
		child.queue_free()

	var IMAGE_BASE_PATH = "res://assets/images/" + folder_name

	# Add clear card at the beginning for all categories except shirts, pants, and emotion
	if folder_name not in ["shirts", "pants", "emotion"]:
		var clear_card = SKIN_CARD_CLEAR_SCENE.instantiate()
		clear_card.name = "ClearSkinCard"
		clear_card.clear_skin.connect(_on_clear_skin)
		item_grid.add_child(clear_card)

	var dir_path = "user://scenes/NewPlayer/resources/".path_join(folder_name)
	var dir = DirAccess.open(dir_path)

	if dir:
		var files = Array(dir.get_files())
		files = files.filter(func(f): return f.ends_with(".tres"))
		files.sort_custom(func(a, b):
			return a.get_basename().to_int() < b.get_basename().to_int()
		)

		# --- NEW LOGIC: Get the currently equipped skin for this category ---
		var category_index = CATEGORY_TO_INDEX.get(folder_name, -1)
		if category_index == -1:
			push_error("Category name '%s' not found in CATEGORY_TO_INDEX map." % folder_name)
			return

		var equipped_skin_index = Global.skin_parameters[category_index]

		for i in range(files.size()):
			var file_name = files[i]
			var card_skin_index = file_name.get_basename().to_int()

			var new_card = SKIN_CARD_SCENE.instantiate()
			new_card.item_selected.connect(_on_item_selected)
			item_grid.add_child(new_card)

			new_card.label.text = str(card_skin_index)

			# --- NEW LOGIC: Check if this card is the currently equipped one ---
			if card_skin_index == equipped_skin_index:
				new_card.price.text = "FREE" # or "0"
				new_card.coin_icon.hide()
			else:
				# Your existing rarity and pricing logic for non-equipped items
				match folder_name:
					"hat":
						if i >= files.size() - 2:
							new_card.rarity = new_card.SKIN_CARD.RARE
					"glasses":
						if i >= files.size() - 3:
							new_card.rarity = new_card.SKIN_CARD.RARE

				var price_value = new_card.price_table[new_card.rarity]
				new_card.price.text = str(price_value)

			# Image loading logic (remains the same)
			var image_path = IMAGE_BASE_PATH.path_join(str(card_skin_index) + ".webp")
			if ResourceLoader.exists(image_path):
				new_card.texture_rect.texture = load(image_path)
			else:
				printerr("Image not found: %s" % image_path)
	else:
		printerr("Failed to open directory: %s" % dir_path)


func _on_copy_user_name_pressed() -> void:
	DisplayServer.clipboard_set(user_name.text)
	copy_user_name.visible = false
	copy_check.visible = true
	get_tree().create_timer(2.0).timeout.connect(
		func():
			copy_check.visible = false
			copy_user_name.visible = true
	)

func handle_gallery_pressed():
	print("Gallery button was pressed!")
	# Add your logic here for what should happen in the Profile scene

func _on_edit_user_name_pressed() -> void:
	Tcpserver.send_request(
		"changenamereq"
	)

func show_change_name():
	if Global.can_change_name:
		Global.user_name = user_name.text
		var change_username_display = change_username_hud.instantiate()
		change_username_display.connect("change_name", Callable(self, "_on_name_changed"))
		add_child(change_username_display)
		

		
func _on_name_changed() -> void:
	user_name.text = Global.global_player_name


func _on_change_bio_pressed() -> void:
	Tcpserver.send_request(
		"changebioreq"
	)
	
func show_change_bio():
	if Global.can_change_bio:
		Global.bio = bio.text
		var change_bio_display = change_bio_hud.instantiate()
		add_child(change_bio_display)
		
func show_change_pic():
	if Global.can_change_pic:
		var change_profile_display = change_profile_hud.instantiate()
		add_child(change_profile_display)
		

func _on_change_profile_pressed() -> void:
	Tcpserver.send_request(
		"changepicreq"
	)
	#var change_profile_display = change_profile_hud.instantiate()
	#add_child(change_profile_display)


func _on_button_pressed() -> void:
	var message = message_hud.instantiate()
	add_child(message)
	message.connect("accepted" , Callable(self,"_on_skin_accepted"))
	message.message.text = "Set new skin ?"
	#set_model_skin.emit()
