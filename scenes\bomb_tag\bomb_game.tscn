[gd_scene load_steps=80 format=4 uid="uid://d1ifvpi5brrwt"]

[ext_resource type="Script" uid="uid://lxwve75xxcv8" path="res://scripts/BombTag_game/game.gd" id="1_2isbr"]
[ext_resource type="Material" uid="uid://da188d7yrcfr8" path="res://scenes/bomb_tag/level/platforms.tres" id="9_yu6lg"]
[ext_resource type="Texture2D" uid="uid://du7bk0ux83jcg" path="res://Images/stone_wall_03_color_1k.png" id="10_0sx3y"]
[ext_resource type="Script" uid="uid://bmtfr28wun4gc" path="res://scripts/BombTag_game/spawn_points.gd" id="12_r0uwu"]
[ext_resource type="Texture2D" uid="uid://bf2jg5q0xqg1a" path="res://Images/SandG_001.jpg" id="13_hqmqs"]
[ext_resource type="PackedScene" uid="uid://b21kpsmngkpeq" path="res://trampoline.tscn" id="14_nuc2m"]
[ext_resource type="PackedScene" uid="uid://cwvpao8htuhk6" path="res://beach/dock_single.tscn" id="15_jg0gx"]
[ext_resource type="PackedScene" uid="uid://gqd5yxceyoih" path="res://beach/palm_tree.tscn" id="16_k2g4h"]
[ext_resource type="PackedScene" uid="uid://d20c2vkvl5c0e" path="res://beach/palm_tree_2.tscn" id="17_j7amm"]
[ext_resource type="PackedScene" path="res://beach/umbrella.tscn" id="18_hjokn"]
[ext_resource type="PackedScene" uid="uid://coinib8qssshp" path="res://beach/umbrella2.tscn" id="19_wmr3b"]
[ext_resource type="PackedScene" uid="uid://xqpck7mfgwsw" path="res://beach/dock.tscn" id="20_43v55"]
[ext_resource type="PackedScene" path="res://beach/crate.tscn" id="21_7gt2u"]
[ext_resource type="PackedScene" uid="uid://4wt7r1f0i0jl" path="res://beach/shipping_port_big.tscn" id="22_bc744"]
[ext_resource type="PackedScene" path="res://beach/rock_large.tscn" id="23_jggqy"]
[ext_resource type="PackedScene" uid="uid://vosng73cptsl" path="res://glue/glue.tscn" id="24_2isbr"]
[ext_resource type="PackedScene" uid="uid://ck6saj0aumi6d" path="res://beach/bush.tscn" id="24_cjhsw"]
[ext_resource type="Script" uid="uid://pjx4121v60s7" path="res://scripts/multiplayer/multiplayer_spawner.gd" id="25_tutp8"]
[ext_resource type="PackedScene" uid="uid://cm26nto6fstcx" path="res://scenes/NewPlayer/Player.tscn" id="26_5epq8"]
[ext_resource type="FontFile" uid="uid://po7n2ndi3l2v" path="res://addons/toastparty/fonts/KnightWarrior-w16n8.ttf" id="27_54mir"]
[ext_resource type="Script" uid="uid://cq74gqxn31lp7" path="res://scripts/BombTag_game/counter.gd" id="27_hfixu"]
[ext_resource type="PackedScene" uid="uid://bc13xpsu2fpb5" path="res://scenes/bomb_tag/explosion.tscn" id="28_rvdiq"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="29_cnxcx"]
[ext_resource type="PackedScene" uid="uid://18yd5e7o4a8u" path="res://power_up_speed.tscn" id="30_2isbr"]
[ext_resource type="Script" uid="uid://cfouqj2eugbat" path="res://scripts/UI/jump_button.gd" id="30_7tpd1"]
[ext_resource type="AudioStream" uid="uid://b7xw7jykngvv0" path="res://assets/SFX/countdown.wav" id="31_2isbr"]
[ext_resource type="Texture2D" uid="uid://v7u0ts8h5rs4" path="res://assets/icons/ingame_ui/jump.png" id="31_nrlga"]
[ext_resource type="PackedScene" uid="uid://ccvulogm0ka2g" path="res://power_up_time.tscn" id="31_ubh3g"]
[ext_resource type="AudioStream" uid="uid://bhlg1wlbgtd4s" path="res://assets/SFX/untitled.wav" id="32_mb8xf"]
[ext_resource type="AudioStream" uid="uid://d07q3f4tjljnb" path="res://assets/SFX/WIN.wav" id="33_ivckt"]
[ext_resource type="Shader" uid="uid://yj6bs51fsu4t" path="res://shaders/sky.gdshader" id="34_gfusy"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_yna5g"]
sky_top_color = Color(0.228725, 0.486073, 0.75, 1)
sky_horizon_color = Color(0.543182, 0.630007, 0.6708, 1)
sky_curve = 0.0861524
ground_bottom_color = Color(0.227451, 0.486275, 0.74902, 1)
ground_horizon_color = Color(0.545098, 0.631373, 0.670588, 1)
ground_curve = 0.0207053

[sub_resource type="Sky" id="Sky_eurag"]
sky_material = SubResource("ProceduralSkyMaterial_yna5g")

[sub_resource type="Environment" id="Environment_54mir"]
background_mode = 2
background_energy_multiplier = 1.5
sky = SubResource("Sky_eurag")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
ambient_light_energy = 1.3

[sub_resource type="ShaderMaterial" id="ShaderMaterial_2isbr"]
render_priority = 0
shader = ExtResource("34_gfusy")
shader_parameter/curve_amount = 0.0
shader_parameter/cloud_density = 0.568
shader_parameter/cloud_speed = Vector2(0.05, 0)
shader_parameter/scale_x = 20.0
shader_parameter/scale_y = 15.41
shader_parameter/softness = 0.07

[sub_resource type="SphereMesh" id="SphereMesh_ubh3g"]
flip_faces = true
radius = 20.0
height = 15.0
is_hemisphere = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_80b28"]
albedo_texture = ExtResource("10_0sx3y")
uv1_scale = Vector3(1.2, 1.2, 1.2)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_61kcn"]
albedo_texture = ExtResource("10_0sx3y")
uv1_scale = Vector3(2, 2, 2)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_36okb"]
albedo_color = Color(0.8, 0.792157, 0.788235, 1)
albedo_texture = ExtResource("13_hqmqs")
uv1_scale = Vector3(0.1, 0.1, 0.1)
uv1_triplanar = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_yceoq"]
albedo_color = Color(0.800781, 0.792986, 0.786792, 1)
albedo_texture = ExtResource("13_hqmqs")
uv1_scale = Vector3(0.2, 0.2, 0.2)
uv1_triplanar = true

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_gfusy"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_ivckt"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1.5, 1.25, 1.25, 1.25)]
op_type = 2
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_6h7qy"]
constant = 0.9

[sub_resource type="FastNoiseLite" id="FastNoiseLite_sqlur"]
noise_type = 2
seed = 5
frequency = 0.1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_tkl2m"]
seamless = true
noise = SubResource("FastNoiseLite_sqlur")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_b2clu"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_iv23c"]
constant = 0.8

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_yu6lg"]
output_port_for_preview = 0
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0.0744019, 0.532943, 0.999944, 1)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_0sx3y"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_ejp6o"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_r0uwu"]
default_input_values = [1, Vector2(0.01, 0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_hqmqs"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_nuc2m"]
default_input_values = [1, Vector2(-0.01, -0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_jg0gx"]
input_name = "time"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_k2g4h"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_j7amm"]
default_input_values = [1, Vector2(0.025, 0.025), 2, Vector2(0, 0)]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_864xt"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_47dmq"]
seamless = true
noise = SubResource("FastNoiseLite_864xt")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_hjokn"]
texture = SubResource("NoiseTexture2D_47dmq")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_wmr3b"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_43v55"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_7gt2u"]
default_input_values = [0, Vector4(0, 0, 0, 0), 1, Vector4(1, 1, 1, 1), 2, Vector4(0, 0, 0, 0)]
op_type = 3

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_bc744"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(5, 1.5, 0.5)]
operator = 2

[sub_resource type="VisualShader" id="VisualShader_jggqy"]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_4;
uniform vec4 ColorParameter : source_color = vec4(0.074402, 0.532943, 0.999944, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_15;



void vertex() {
// Input:2
	float n_out2p0 = TIME;


// UVFunc:3
	vec2 n_in3p1 = vec2(0.02500, 0.02500);
	vec2 n_out3p0 = vec2(n_out2p0) * n_in3p1 + UV;


// Texture2D:4
	vec4 n_out4p0 = texture(tex_vtx_4, n_out3p0);


// Input:6
	vec3 n_out6p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(5.00000, 1.50000, 0.50000);
	vec3 n_out8p0 = n_out6p0 * n_in8p1;


// Input:5
	vec3 n_out5p0 = VERTEX;


// MultiplyAdd:7
	vec4 n_out7p0 = (n_out4p0 * vec4(n_out8p0, 0.0)) + vec4(n_out5p0, 0.0);


// Output:0
	VERTEX = vec3(n_out7p0.xyz);


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.01000, 0.01000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:9
	float n_out9p0 = TIME;


// UVFunc:8
	vec2 n_in8p1 = vec2(-0.01000, -0.01000);
	vec2 n_out8p0 = vec2(n_out9p0) * n_in8p1 + UV;


// Texture2D:15
	vec4 n_out15p0 = texture(tex_frg_15, n_out8p0);


// VectorOp:10
	vec4 n_out10p0 = n_out3p0 * n_out15p0;


// VectorOp:11
	vec4 n_in11p1 = vec4(1.50000, 1.25000, 1.25000, 1.25000);
	vec4 n_out11p0 = pow(n_out10p0, n_in11p1);


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 + n_out11p0;


// FloatConstant:16
	float n_out16p0 = 0.800000;


// FloatConstant:13
	float n_out13p0 = 0.900000;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ALPHA = n_out16p0;
	ROUGHNESS = n_out13p0;


}
"
nodes/vertex/0/position = Vector2(640, 120)
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_k2g4h")
nodes/vertex/2/position = Vector2(-552.787, -31.8079)
nodes/vertex/3/node = SubResource("VisualShaderNodeUVFunc_j7amm")
nodes/vertex/3/position = Vector2(-132.787, -31.8079)
nodes/vertex/4/node = SubResource("VisualShaderNodeTexture_hjokn")
nodes/vertex/4/position = Vector2(147.213, -11.8079)
nodes/vertex/5/node = SubResource("VisualShaderNodeInput_wmr3b")
nodes/vertex/5/position = Vector2(-552.787, 488.192)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_43v55")
nodes/vertex/6/position = Vector2(-532.787, 228.192)
nodes/vertex/7/node = SubResource("VisualShaderNodeMultiplyAdd_7gt2u")
nodes/vertex/7/position = Vector2(387.213, 408.192)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_bc744")
nodes/vertex/8/position = Vector2(-172.787, 348.192)
nodes/vertex/connections = PackedInt32Array(2, 0, 3, 2, 3, 0, 4, 0, 4, 0, 7, 0, 5, 0, 7, 2, 6, 0, 8, 0, 8, 0, 7, 1, 7, 0, 0, 0)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_yu6lg")
nodes/fragment/2/position = Vector2(-720, 40)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_0sx3y")
nodes/fragment/3/position = Vector2(-1560, -20)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_ejp6o")
nodes/fragment/4/position = Vector2(-160, 180)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_r0uwu")
nodes/fragment/5/position = Vector2(-1960, -20)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_hqmqs")
nodes/fragment/6/position = Vector2(-2440, 60)
nodes/fragment/8/node = SubResource("VisualShaderNodeUVFunc_nuc2m")
nodes/fragment/8/position = Vector2(-1960, 380)
nodes/fragment/9/node = SubResource("VisualShaderNodeInput_jg0gx")
nodes/fragment/9/position = Vector2(-2440, 460)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_gfusy")
nodes/fragment/10/position = Vector2(-980, 280)
nodes/fragment/11/node = SubResource("VisualShaderNodeVectorOp_ivckt")
nodes/fragment/11/position = Vector2(-700, 340)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_6h7qy")
nodes/fragment/13/position = Vector2(-160, 660)
nodes/fragment/15/node = SubResource("VisualShaderNodeTexture_b2clu")
nodes/fragment/15/position = Vector2(-1600, 340)
nodes/fragment/16/node = SubResource("VisualShaderNodeFloatConstant_iv23c")
nodes/fragment/16/position = Vector2(-160, 560)
nodes/fragment/connections = PackedInt32Array(5, 0, 3, 0, 6, 0, 5, 2, 9, 0, 8, 2, 3, 0, 10, 0, 10, 0, 11, 0, 2, 0, 4, 0, 11, 0, 4, 1, 4, 0, 0, 0, 13, 0, 0, 3, 8, 0, 15, 0, 15, 0, 10, 1, 16, 0, 0, 1)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_co1eh"]
render_priority = 0
shader = SubResource("VisualShader_jggqy")
shader_parameter/ColorParameter = Color(0, 0.41217, 0.667969, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_4h7cg"]
material = SubResource("ShaderMaterial_co1eh")
size = Vector2(800, 800)
subdivide_width = 32
subdivide_depth = 32

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_p2ijq"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ivckt"]
data = PackedVector3Array(0.452, 0.2829, -0.43, 0.452, 0.2829, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, -0.43, 0.452, 0.2829, -0.43, -0.43, 0.1242, -0.4779, -0.43, 0.0621, -0.4779, 0.43, 0.0621, -0.4779, 0.43, 0.0621, -0.4779, 0.43, 0.1242, -0.4779, -0.43, 0.1242, -0.4779, 0.4779, 0.0621, -0.43, 0.4779, 0.0621, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.0621, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2829, -0.43, 0.4779, 0.2208, -0.43, 0.43, 0.2829, -0.4521, 0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.4521, 0.452, 0.1242, -0.43, 0.452, 0.1242, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, -0.43, 0.452, 0.1242, -0.43, -0.43, 0.2829, -0.4779, -0.43, 0.2208, -0.4779, 0.43, 0.2208, -0.4779, 0.43, 0.2208, -0.4779, 0.43, 0.2829, -0.4779, -0.43, 0.2829, -0.4779, 0.43, 0.1242, -0.4521, 0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.4521, 0.4779, 0.1242, 0.43, 0.452, 0.1242, 0.43, 0.452, 0.1242, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.1242, 0.43, 0.43, 0.0621, -0.478, -0.43, 0.0621, -0.478, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, 0.43, 0.0621, -0.4521, 0.43, 0.0621, -0.478, 0.5, 0, 0.43, 0.43, 0, 0.43, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.5, 0, 0.5, 0.5, 0, 0.43, 0.43, 0.345, -0.5, 0.43, 0, -0.5, 0.5, 0, -0.5, 0.5, 0, -0.5, 0.5, 0.345, -0.5, 0.43, 0.345, -0.5, 0.5, 0.345, 0.5, 0.5, 0, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.43, 0.345, 0.5, 0.5, 0.345, 0.5, -0.43, 0.1242, -0.478, -0.43, 0.1242, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.478, -0.43, 0.1242, -0.478, -0.43, 0.2829, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.478, -0.43, 0.2208, -0.478, -0.43, 0, -0.5, -0.43, 0.0621, -0.478, -0.43, 0.1242, -0.478, -0.43, 0, -0.5, -0.43, 0.2208, -0.478, -0.43, 0.2829, -0.478, -0.43, 0, -0.5, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.478, -0.43, 0, -0.5, -0.43, 0.2829, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.345, -0.5, -0.43, 0, -0.5, -0.43, 0.2829, -0.478, -0.43, 0.345, -0.5, -0.43, 0.2829, -0.478, -0.43, 0.2829, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0, -0.5, -0.43, 0, -0.43, -0.43, 0.345, -0.43, -0.43, 0.345, -0.5, -0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.345, -0.43, -0.43, 0, -0.43, -0.43, 0.345, -0.43, -0.43, 0.0621, -0.4521, 0.43, 0, 0.43, 0.5, 0, 0.43, 0.452, 0.0621, 0.43, 0.4779, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.5, 0, 0.43, 0.452, 0.0621, 0.43, 0.43, 0.345, 0.43, 0.43, 0, 0.43, 0.5, 0, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.0621, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.1242, 0.43, 0.43, 0.345, 0.43, 0.452, 0.1242, 0.43, 0.4779, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.43, 0.345, 0.43, 0.4779, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.4779, 0.1242, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2829, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2208, 0.43, 0.452, 0.2829, 0.43, 0.4779, 0.2829, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.43, 0.345, 0.43, 0.4779, 0.2829, 0.43, 0.43, 0.2208, -0.478, -0.43, 0.2208, -0.478, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.478, 0.43, 0.345, 0.43, 0.43, 0.345, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.43, 0.43, 0.345, 0.43, -0.43, 0, -0.5, -0.5, 0, -0.5, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.43, 0, -0.43, -0.43, 0, -0.5, 0.5, 0, -0.5, 0.5, 0, -0.43, 0.5, 0.345, -0.43, 0.5, 0.345, -0.43, 0.5, 0.345, -0.5, 0.5, 0, -0.5, -0.5, 0.345, -0.5, -0.5, 0.345, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.5, -0.5, 0.345, -0.5, 0.4779, 0.2208, -0.43, 0.452, 0.2208, -0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.2208, -0.43, 0.5, 0, 0.43, 0.5, 0, 0.5, 0.5, 0.345, 0.5, 0.5, 0.345, 0.5, 0.5, 0.345, 0.43, 0.5, 0, 0.43, 0.4779, 0.2829, 0.43, 0.452, 0.2829, 0.43, 0.452, 0.2829, -0.43, 0.452, 0.2829, -0.43, 0.4779, 0.2829, -0.43, 0.4779, 0.2829, 0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.43, 0.345, -0.5, 0.43, 0.345, -0.5, 0.5, 0.345, -0.5, 0.5, 0.345, -0.43, -0.5, 0.345, -0.5, -0.5, 0, -0.5, -0.43, 0, -0.5, -0.43, 0, -0.5, -0.43, 0.345, -0.5, -0.5, 0.345, -0.5, 0.43, 0.345, -0.43, 0.43, 0, -0.43, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.345, -0.5, 0.43, 0.345, -0.43, 0.43, 0.0621, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0, -0.43, 0.43, 0, -0.43, 0.43, 0, -0.5, 0.43, 0.0621, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.478, 0.43, 0.345, -0.5, 0.43, 0.0621, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.345, -0.5, 0.43, 0, -0.5, 0.43, 0.0621, -0.478, 0.43, 0.0621, -0.4521, 0.43, 0, -0.5, 0.43, 0.345, -0.5, 0.43, 0.0621, -0.478, 0.43, 0.0621, -0.4521, 0.43, 0.1242, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.478, 0.43, 0.2208, -0.4521, 0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.478, 0.43, 0.0621, -0.478, 0.43, 0.345, -0.5, 0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.1242, -0.478, 0.43, 0.345, -0.5, -0.43, 0.345, -0.43, -0.43, 0, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.5, 0.345, -0.43, -0.43, 0.345, -0.43, 0.5, 0, -0.5, 0.43, 0, -0.5, 0.43, 0, -0.43, 0.43, 0, -0.43, 0.5, 0, -0.43, 0.5, 0, -0.5, 0.4779, 0.0621, -0.43, 0.452, 0.0621, -0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.4779, 0.0621, 0.43, 0.4779, 0.0621, -0.43, 0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.478, -0.43, 0.1242, -0.478, 0.43, 0.1242, -0.478, 0.43, 0.1242, -0.4521, 0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.478, -0.43, 0.2829, -0.478, 0.43, 0.2829, -0.478, 0.43, 0.2829, -0.4521, 0.5, 0, -0.43, 0.43, 0, -0.43, 0.4779, 0.0621, -0.43, 0.452, 0.0621, -0.43, 0.4779, 0.0621, -0.43, 0.43, 0, -0.43, 0.4779, 0.0621, -0.43, 0.5, 0.345, -0.43, 0.5, 0, -0.43, 0.43, 0, -0.43, 0.43, 0.345, -0.43, 0.452, 0.0621, -0.43, 0.43, 0.345, -0.43, 0.452, 0.1242, -0.43, 0.452, 0.0621, -0.43, 0.4779, 0.0621, -0.43, 0.4779, 0.1242, -0.43, 0.5, 0.345, -0.43, 0.4779, 0.1242, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.5, 0.345, -0.43, 0.452, 0.2208, -0.43, 0.4779, 0.2208, -0.43, 0.452, 0.1242, -0.43, 0.43, 0.345, -0.43, 0.452, 0.2208, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.2829, -0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.452, 0.2829, -0.43, 0.452, 0.2208, -0.43, 0.4779, 0.2829, -0.43, 0.452, 0.2829, -0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.5, 0.345, -0.43, 0.452, 0.2829, -0.43, -0.43, 0.345, -0.43, -0.5, 0.345, -0.43, -0.5, 0.345, -0.5, -0.5, 0.345, -0.5, -0.43, 0.345, -0.5, -0.43, 0.345, -0.43, 0.5, 0.345, 0.5, 0.43, 0.345, 0.5, 0.43, 0.345, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.5, 0.345, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6h7qy"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_b2clu"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="ArrayMesh" id="ArrayMesh_iv23c"]
_surfaces = [{
"aabb": AABB(-0.43, 0.0621049, -0.47795, 0.90795, 0.220817, 0.90795),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QNAACx+P////8AALH4/7f//wAAsfj/t4QNAAAAAP9HAAAAAAAAAAAAAAAAevIAAAAAAAB68v9HAAAAAP//AACEDQAA//8AAP//AAD///9H//8AAP///0eEDQAA////t4QNAAD///+3//8AAP///////wAA/////4QNAAB68v//TQcAAHry/7dNBwAAAAD/t00HAAAAAP//TQcAALH4/0eEDQAAsfj/R///AACx+AAA//8AALH4AACEDQAAAAD//wAAAAAAAP+3AAAAAHry/7cAAAAAevL//wAAAAB68v9HTQcAAHryAABNBwAAAAAAAE0HAAAAAP9HTQcAAA==")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.345027, 1),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAAAQAAAAQAAwACAAUAAQAEAAYABwAEAAAABAAHAAUACAAHAAAABQAJAAQACQAFAAIABAAKAAYACwAJAAIACAAAAAwACwACAA0ACAAOAAcACgAOAAYACAAGAA4ADgAKAA8ACwAPAAkADwAQAA4AEAAPABEABQAQABEACwARAA8AAwAFABEAEgADABEAEwALAA0ADQASABMAEgANABQAFQATABIAEgARABUAFAAMABIAFgAVABEAEwAVABYAEQAXABYAGAAXABEAFgAXABgAEQALABgADAAUABkAGQAIAAwAFgAaABMAGAAaABYACwATABsAGwAYAAsAEwAaABwAHAAbABMAGgAdABwAGgAYAB0AHAAeABsAGwAfABgAIAAcAB0AIQAfABsAHAAiAB4AIwAdABgAHwAkABgAJAAjABgAJAAeACIAIgAjACQAJQAeACQAJgAjACIAJAAnACUAJQAnACYAIgAoACYAJgAoACUAKQAlACgAGwApACEAKQAoACAAJgAnACoAJwAhACoAKwAmACoAHQArACAAIAArACoALAAhACkALAAqACEAKQAgAC0ALQAsACkAIAAqAC4ALgAtACAAKgAsAC8ALwAsAC0AMAAuACoALQAuADAALwAxACoAKgAxADAAMAAxAC8ALQAyAC8ALwAyADAAMAAyAC0AGQAzADQAMwAZABQAFAAGADMANAAzAAYAFAABAAYANAA1ABkAGQA1ADYANgA1ADQANgAIABkABgAIADYANAA3ADYANgA3AAYABgA3ADQA"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("WfooXBPuAAC48yhcE+4AALjzKFzrEQAAWfooXOsRAAC489ajE+4AAFn61qPrEQAAE+7//xPuAABZ+tajE+4AAP////8T7gAAuPPWo+sRAAC48+rRE+4AABPu///rEQAAWfoULhPuAAC48xQu6xEAAFn66tET7gAAuPPq0esRAABZ+urR6xEAAP/////rEQAAWfoULusRAAAT7gAA6xEAALjzFC4T7gAA//8AAOsRAAD//wAAAAAAAP////8AAAAAE+7//wAAAAD//wAAE+4AABPuAAAAAAAAE+7q0UYMAAAT7hQuRgwAABPuFC6lBQAAE+7Wo0YMAAAT7urRpQUAAOsRFC5GDAAA6xHq0aUFAAAT7ihcRgwAABPuKFylBQAAE+7Wo6UFAADrEdajRgwAAOsRKFylBQAA6xHWo6UFAADrEShcRgwAAOsR6tFGDAAA6xEAAAAAAADrERQupQUAAOsR//8AAAAA6xH//+sRAADrEQAA6xEAAAAA//8AAAAAAAAAAOsRAAAAAAAAAAAAAAAA///rEQAAE+4AABPuAAAT7gAA//8AAP//AAD//wAA////////AAAT7v////8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_yu6lg"]
resource_name = "fence_corner_Mesh fence_corner"
_surfaces = [{
"aabb": AABB(-0.43, 0.0621049, -0.47795, 0.90795, 0.220817, 0.90795),
"attribute_data": PackedByteArray("AAAAAP//AAD//94eAADeHv//4k7//8FtAADBbQAA4k7//8FtAADBbQAA4k7//+JO///eHgAA3h4AAAAA//8AAP//AAD//94eAADeHgAAAAAAAOJO///iTv//wW0AAMFt//8AAP//3h4AAN4eAAAAAP//4k7//8FtAADBbQAA4k4="),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"material": SubResource("StandardMaterial3D_6h7qy"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(33.8583, 20.2774, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QN/7+x+P//////v7H4/7f///+/sfj/t4QN/78AAP9HAAD//wAAAAAAAP//evIAAAAA//968v9HAAD/////AACEDf+///8AAP///7////9H////v////0eEDf+/////t4QN/7////+3////v/////////+//////4QN/7968v//TQcAgHry/7dNBwCAAAD/t00HAIAAAP//TQcAgLH4/0eEDf+/sfj/R////7+x+AAA////v7H4AACEDf+/AAD//wAA//8AAP+3AAD//3ry/7cAAP//evL//wAA//968v9HTQcAgHryAABNBwCAAAAAAE0HAIAAAP9HTQcAgP9/////f////3////9/////f////3////9/////f////38AAP9/AAD/fwAA/38AAP9/AAD/fwAA/38AAP9/AAD/f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f///")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.345027, 1),
"attribute_data": PackedByteArray("Wfrx7rjz8e64820dWfptHesRvxET7r8RE+4OGOsRDhgAAPHu6xHx7usR//8AAP//6xEhMusRL4YAAC+GAAAhMv//ITL//y+GE+4vhhPuITJZ+u1nuPPtZ7jzY1BZ+mNQuPNCQf//L4ZZ+g53WfpCQbjzDnf//yEyE+4vhhPuITLrES+GAAAvhkYMDnelBQ536xEhMgAAITKlBe1nRgztZ0YMY1ClBWNQRgxCQaUFQkHrEb8RE+6/ERPuDhjrEQ4YE+4hMv//ITL//y+GE+4vhhPuYAz//2AM//9tHRPubR3//y+GE+4vhhPuITL//yEyAAAhMusRITLrES+GAAAvhqUFbR1GDG0dRgzx7qUF8e7rES+GAAAvhgAAITLrESEyWfrx7rjz8e64820dWfptHf//bR0T7m0dE+5gDP//YAz//yEy//8vhhPuL4YT7iEy6xEhMusRL4ZGDEJBAAAhMkYMDncAAC+GpQVCQUYMY1ClBWNQpQUOd0YM7WelBe1n6xEhMusRL4YAAC+GAAAhMgAAYAzrEWAM6xFtHQAAbR2lBW0dRgxtHUYM8e6lBfHuE+4OGOsRDhjrEb8RE+6/ERPuDhjrEQ4Y6xG/ERPuvxH//y+GE+4vhln6Dne48w53//8hMhPuITK48+1nWfrtZ1n6Y1C482NQWfpCQbjzQkHrEW0dAABtHQAAYAzrEWAM/////xPu//8T7vHu///x7g=="),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAWABUAFAAXABkAGgAUABkAFwAbABkAHAAaABkAGAAVABwAHQAZABsAHQAbABgAHAAZAB4AHwAdABgAGAAcAB8AHgAfABwAIAAhACIAIwAiACEAIgAkACAAIQAlACMAJQAmACMAIgAnACQAJwAmACgAJwAoACQAKQAoACYAJQApACYAKAAqACQAJQArACkAKgArACQAJQAkACsALAAtAC4ALgAvACwAMAAxADIAMgAzADAANAA1ADYANgA3ADQAOAA5ADoAOgA7ADgAPAA9AD4APgA/ADwAQABBAEIAQgBDAEAARABFAEYARgBHAEQASABJAEoASgBLAEgATABNAE4ATgBPAEwAUABRAFIAUgBTAFAAVABVAFYAVgBXAFQAWABWAFUAVQBZAFgAVgBaAFcAWABbAFYAWgBcAFcAWQBdAFgAWQBXAF0AWABeAFsAXABbAF4AXwBdAFcAXgBfAFwAXABfAFcAYABhAGIAYgBjAGAAZABlAGYAZgBnAGQAaABpAGoAagBrAGgAbABtAG4AbgBvAGwAcABxAHIAcgBzAHAAdAB1AHYAdwB2AHUAdgB4AHQAdQB5AHcAeQB6AHcAdgB7AHgAewB6AHwAewB8AHgAfQB8AHoAeQB9AHoAfAB+AHgAeQB/AH0AfgB/AHgAeQB4AH8AgACBAIIAggCDAIAAhACFAIYAhgCHAIQA"),
"material": SubResource("StandardMaterial3D_b2clu"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(39.3701, 41.3701, 0, 0),
"vertex_count": 136,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_iv23c")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_6h7qy"]
data = PackedVector3Array(0.452, 0.2829, -0.43, 0.452, 0.2829, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, -0.43, 0.452, 0.2829, -0.43, -0.43, 0.1242, -0.4779, -0.43, 0.0621, -0.4779, 0.43, 0.0621, -0.4779, 0.43, 0.0621, -0.4779, 0.43, 0.1242, -0.4779, -0.43, 0.1242, -0.4779, 0.4779, 0.0621, -0.43, 0.4779, 0.0621, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.0621, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2829, -0.43, 0.4779, 0.2208, -0.43, 0.43, 0.2829, -0.4521, 0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.4521, 0.452, 0.1242, -0.43, 0.452, 0.1242, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, -0.43, 0.452, 0.1242, -0.43, -0.43, 0.2829, -0.4779, -0.43, 0.2208, -0.4779, 0.43, 0.2208, -0.4779, 0.43, 0.2208, -0.4779, 0.43, 0.2829, -0.4779, -0.43, 0.2829, -0.4779, 0.43, 0.1242, -0.4521, 0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.4521, 0.4779, 0.1242, 0.43, 0.452, 0.1242, 0.43, 0.452, 0.1242, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.1242, 0.43, 0.43, 0.0621, -0.478, -0.43, 0.0621, -0.478, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.4521, 0.43, 0.0621, -0.4521, 0.43, 0.0621, -0.478, 0.5, 0, 0.43, 0.43, 0, 0.43, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.5, 0, 0.5, 0.5, 0, 0.43, 0.43, 0.345, -0.5, 0.43, 0, -0.5, 0.5, 0, -0.5, 0.5, 0, -0.5, 0.5, 0.345, -0.5, 0.43, 0.345, -0.5, 0.5, 0.345, 0.5, 0.5, 0, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.43, 0.345, 0.5, 0.5, 0.345, 0.5, -0.43, 0.1242, -0.478, -0.43, 0.1242, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.478, -0.43, 0.1242, -0.478, -0.43, 0.2829, -0.4521, -0.43, 0.2208, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.478, -0.43, 0.2208, -0.478, -0.43, 0, -0.5, -0.43, 0.0621, -0.478, -0.43, 0.1242, -0.478, -0.43, 0, -0.5, -0.43, 0.2208, -0.478, -0.43, 0.2829, -0.478, -0.43, 0, -0.5, -0.43, 0.0621, -0.4521, -0.43, 0.0621, -0.478, -0.43, 0, -0.5, -0.43, 0.2829, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.345, -0.5, -0.43, 0, -0.5, -0.43, 0.2829, -0.478, -0.43, 0.345, -0.5, -0.43, 0.2829, -0.478, -0.43, 0.2829, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0, -0.5, -0.43, 0, -0.43, -0.43, 0.345, -0.43, -0.43, 0.345, -0.5, -0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.4521, -0.43, 0.0621, -0.4521, -0.43, 0.345, -0.43, -0.43, 0, -0.43, -0.43, 0.345, -0.43, -0.43, 0.0621, -0.4521, 0.43, 0, 0.43, 0.5, 0, 0.43, 0.452, 0.0621, 0.43, 0.4779, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.5, 0, 0.43, 0.452, 0.0621, 0.43, 0.43, 0.345, 0.43, 0.43, 0, 0.43, 0.5, 0, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.0621, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.1242, 0.43, 0.4779, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.452, 0.1242, 0.43, 0.43, 0.345, 0.43, 0.452, 0.1242, 0.43, 0.4779, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.43, 0.345, 0.43, 0.4779, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.4779, 0.1242, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.1242, 0.43, 0.452, 0.2208, 0.43, 0.452, 0.2829, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.4779, 0.2829, 0.43, 0.4779, 0.2208, 0.43, 0.452, 0.2829, 0.43, 0.4779, 0.2829, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.43, 0.345, 0.43, 0.4779, 0.2829, 0.43, 0.43, 0.2208, -0.478, -0.43, 0.2208, -0.478, -0.43, 0.2208, -0.4521, -0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.478, 0.43, 0.345, 0.43, 0.43, 0.345, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.5, 0.43, 0, 0.43, 0.43, 0.345, 0.43, -0.43, 0, -0.5, -0.5, 0, -0.5, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.43, 0, -0.43, -0.43, 0, -0.5, 0.5, 0, -0.5, 0.5, 0, -0.43, 0.5, 0.345, -0.43, 0.5, 0.345, -0.43, 0.5, 0.345, -0.5, 0.5, 0, -0.5, -0.5, 0.345, -0.5, -0.5, 0.345, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.5, -0.5, 0.345, -0.5, 0.4779, 0.2208, -0.43, 0.452, 0.2208, -0.43, 0.452, 0.2208, 0.43, 0.452, 0.2208, 0.43, 0.4779, 0.2208, 0.43, 0.4779, 0.2208, -0.43, 0.5, 0, 0.43, 0.5, 0, 0.5, 0.5, 0.345, 0.5, 0.5, 0.345, 0.5, 0.5, 0.345, 0.43, 0.5, 0, 0.43, 0.4779, 0.2829, 0.43, 0.452, 0.2829, 0.43, 0.452, 0.2829, -0.43, 0.452, 0.2829, -0.43, 0.4779, 0.2829, -0.43, 0.4779, 0.2829, 0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.43, 0.345, -0.5, 0.43, 0.345, -0.5, 0.5, 0.345, -0.5, 0.5, 0.345, -0.43, -0.5, 0.345, -0.5, -0.5, 0, -0.5, -0.43, 0, -0.5, -0.43, 0, -0.5, -0.43, 0.345, -0.5, -0.5, 0.345, -0.5, 0.43, 0.345, -0.43, 0.43, 0, -0.43, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.345, -0.5, 0.43, 0.345, -0.43, 0.43, 0.0621, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0, -0.43, 0.43, 0, -0.43, 0.43, 0, -0.5, 0.43, 0.0621, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.478, 0.43, 0.345, -0.5, 0.43, 0.0621, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2829, -0.4521, 0.43, 0.2829, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.345, -0.5, 0.43, 0, -0.5, 0.43, 0.0621, -0.478, 0.43, 0.0621, -0.4521, 0.43, 0, -0.5, 0.43, 0.345, -0.5, 0.43, 0.0621, -0.478, 0.43, 0.0621, -0.4521, 0.43, 0.1242, -0.4521, 0.43, 0.2208, -0.4521, 0.43, 0.2208, -0.478, 0.43, 0.2208, -0.4521, 0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.478, 0.43, 0.0621, -0.478, 0.43, 0.345, -0.5, 0.43, 0.1242, -0.4521, 0.43, 0.1242, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.2208, -0.478, 0.43, 0.1242, -0.478, 0.43, 0.345, -0.5, -0.43, 0.345, -0.43, -0.43, 0, -0.43, -0.5, 0, -0.43, -0.5, 0, -0.43, -0.5, 0.345, -0.43, -0.43, 0.345, -0.43, 0.5, 0, -0.5, 0.43, 0, -0.5, 0.43, 0, -0.43, 0.43, 0, -0.43, 0.5, 0, -0.43, 0.5, 0, -0.5, 0.4779, 0.0621, -0.43, 0.452, 0.0621, -0.43, 0.452, 0.0621, 0.43, 0.452, 0.0621, 0.43, 0.4779, 0.0621, 0.43, 0.4779, 0.0621, -0.43, 0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.4521, -0.43, 0.1242, -0.478, -0.43, 0.1242, -0.478, 0.43, 0.1242, -0.478, 0.43, 0.1242, -0.4521, 0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.4521, -0.43, 0.2829, -0.478, -0.43, 0.2829, -0.478, 0.43, 0.2829, -0.478, 0.43, 0.2829, -0.4521, 0.5, 0, -0.43, 0.43, 0, -0.43, 0.4779, 0.0621, -0.43, 0.452, 0.0621, -0.43, 0.4779, 0.0621, -0.43, 0.43, 0, -0.43, 0.4779, 0.0621, -0.43, 0.5, 0.345, -0.43, 0.5, 0, -0.43, 0.43, 0, -0.43, 0.43, 0.345, -0.43, 0.452, 0.0621, -0.43, 0.43, 0.345, -0.43, 0.452, 0.1242, -0.43, 0.452, 0.0621, -0.43, 0.4779, 0.0621, -0.43, 0.4779, 0.1242, -0.43, 0.5, 0.345, -0.43, 0.4779, 0.1242, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.5, 0.345, -0.43, 0.452, 0.2208, -0.43, 0.4779, 0.2208, -0.43, 0.452, 0.1242, -0.43, 0.43, 0.345, -0.43, 0.452, 0.2208, -0.43, 0.452, 0.1242, -0.43, 0.4779, 0.2208, -0.43, 0.4779, 0.2829, -0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.452, 0.2829, -0.43, 0.452, 0.2208, -0.43, 0.4779, 0.2829, -0.43, 0.452, 0.2829, -0.43, 0.5, 0.345, -0.43, 0.43, 0.345, -0.43, 0.5, 0.345, -0.43, 0.452, 0.2829, -0.43, -0.43, 0.345, -0.43, -0.5, 0.345, -0.43, -0.5, 0.345, -0.5, -0.5, 0.345, -0.5, -0.43, 0.345, -0.5, -0.43, 0.345, -0.43, 0.5, 0.345, 0.5, 0.43, 0.345, 0.5, 0.43, 0.345, 0.43, 0.43, 0.345, 0.43, 0.5, 0.345, 0.43, 0.5, 0.345, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_iv23c"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_yu6lg"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="ArrayMesh" id="ArrayMesh_r0uwu"]
_surfaces = [{
"aabb": AABB(-0.43, 0.0621049, -0.47795, 0.90795, 0.220817, 0.90795),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QNAACx+P////8AALH4/7f//wAAsfj/t4QNAAAAAP9HAAAAAAAAAAAAAAAAevIAAAAAAAB68v9HAAAAAP//AACEDQAA//8AAP//AAD///9H//8AAP///0eEDQAA////t4QNAAD///+3//8AAP///////wAA/////4QNAAB68v//TQcAAHry/7dNBwAAAAD/t00HAAAAAP//TQcAALH4/0eEDQAAsfj/R///AACx+AAA//8AALH4AACEDQAAAAD//wAAAAAAAP+3AAAAAHry/7cAAAAAevL//wAAAAB68v9HTQcAAHryAABNBwAAAAAAAE0HAAAAAP9HTQcAAA==")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.345027, 1),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAAAQAAAAQAAwACAAUAAQAEAAYABwAEAAAABAAHAAUACAAHAAAABQAJAAQACQAFAAIABAAKAAYACwAJAAIACAAAAAwACwACAA0ACAAOAAcACgAOAAYACAAGAA4ADgAKAA8ACwAPAAkADwAQAA4AEAAPABEABQAQABEACwARAA8AAwAFABEAEgADABEAEwALAA0ADQASABMAEgANABQAFQATABIAEgARABUAFAAMABIAFgAVABEAEwAVABYAEQAXABYAGAAXABEAFgAXABgAEQALABgADAAUABkAGQAIAAwAFgAaABMAGAAaABYACwATABsAGwAYAAsAEwAaABwAHAAbABMAGgAdABwAGgAYAB0AHAAeABsAGwAfABgAIAAcAB0AIQAfABsAHAAiAB4AIwAdABgAHwAkABgAJAAjABgAJAAeACIAIgAjACQAJQAeACQAJgAjACIAJAAnACUAJQAnACYAIgAoACYAJgAoACUAKQAlACgAGwApACEAKQAoACAAJgAnACoAJwAhACoAKwAmACoAHQArACAAIAArACoALAAhACkALAAqACEAKQAgAC0ALQAsACkAIAAqAC4ALgAtACAAKgAsAC8ALwAsAC0AMAAuACoALQAuADAALwAxACoAKgAxADAAMAAxAC8ALQAyAC8ALwAyADAAMAAyAC0AGQAzADQAMwAZABQAFAAGADMANAAzAAYAFAABAAYANAA1ABkAGQA1ADYANgA1ADQANgAIABkABgAIADYANAA3ADYANgA3AAYABgA3ADQA"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("WfooXBPuAAC48yhcE+4AALjzKFzrEQAAWfooXOsRAAC489ajE+4AAFn61qPrEQAAE+7//xPuAABZ+tajE+4AAP////8T7gAAuPPWo+sRAAC48+rRE+4AABPu///rEQAAWfoULhPuAAC48xQu6xEAAFn66tET7gAAuPPq0esRAABZ+urR6xEAAP/////rEQAAWfoULusRAAAT7gAA6xEAALjzFC4T7gAA//8AAOsRAAD//wAAAAAAAP////8AAAAAE+7//wAAAAD//wAAE+4AABPuAAAAAAAAE+7q0UYMAAAT7hQuRgwAABPuFC6lBQAAE+7Wo0YMAAAT7urRpQUAAOsRFC5GDAAA6xHq0aUFAAAT7ihcRgwAABPuKFylBQAAE+7Wo6UFAADrEdajRgwAAOsRKFylBQAA6xHWo6UFAADrEShcRgwAAOsR6tFGDAAA6xEAAAAAAADrERQupQUAAOsR//8AAAAA6xH//+sRAADrEQAA6xEAAAAA//8AAAAAAAAAAOsRAAAAAAAAAAAAAAAA///rEQAAE+4AABPuAAAT7gAA//8AAP//AAD//wAA////////AAAT7v////8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_hqmqs"]
resource_name = "fence_corner_Mesh fence_corner"
_surfaces = [{
"aabb": AABB(-0.43, 0.0621049, -0.47795, 0.90795, 0.220817, 0.90795),
"attribute_data": PackedByteArray("AAAAAP//AAD//94eAADeHv//4k7//8FtAADBbQAA4k7//8FtAADBbQAA4k7//+JO///eHgAA3h4AAAAA//8AAP//AAD//94eAADeHgAAAAAAAOJO///iTv//wW0AAMFt//8AAP//3h4AAN4eAAAAAP//4k7//8FtAADBbQAA4k4="),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"material": SubResource("StandardMaterial3D_iv23c"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(33.8583, 20.2774, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QN/7+x+P//////v7H4/7f///+/sfj/t4QN/78AAP9HAAD//wAAAAAAAP//evIAAAAA//968v9HAAD/////AACEDf+///8AAP///7////9H////v////0eEDf+/////t4QN/7////+3////v/////////+//////4QN/7968v//TQcAgHry/7dNBwCAAAD/t00HAIAAAP//TQcAgLH4/0eEDf+/sfj/R////7+x+AAA////v7H4AACEDf+/AAD//wAA//8AAP+3AAD//3ry/7cAAP//evL//wAA//968v9HTQcAgHryAABNBwCAAAAAAE0HAIAAAP9HTQcAgP9/////f////3////9/////f////3////9/////f////38AAP9/AAD/fwAA/38AAP9/AAD/fwAA/38AAP9/AAD/f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f///")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.345027, 1),
"attribute_data": PackedByteArray("Wfrx7rjz8e64820dWfptHesRvxET7r8RE+4OGOsRDhgAAPHu6xHx7usR//8AAP//6xEhMusRL4YAAC+GAAAhMv//ITL//y+GE+4vhhPuITJZ+u1nuPPtZ7jzY1BZ+mNQuPNCQf//L4ZZ+g53WfpCQbjzDnf//yEyE+4vhhPuITLrES+GAAAvhkYMDnelBQ536xEhMgAAITKlBe1nRgztZ0YMY1ClBWNQRgxCQaUFQkHrEb8RE+6/ERPuDhjrEQ4YE+4hMv//ITL//y+GE+4vhhPuYAz//2AM//9tHRPubR3//y+GE+4vhhPuITL//yEyAAAhMusRITLrES+GAAAvhqUFbR1GDG0dRgzx7qUF8e7rES+GAAAvhgAAITLrESEyWfrx7rjz8e64820dWfptHf//bR0T7m0dE+5gDP//YAz//yEy//8vhhPuL4YT7iEy6xEhMusRL4ZGDEJBAAAhMkYMDncAAC+GpQVCQUYMY1ClBWNQpQUOd0YM7WelBe1n6xEhMusRL4YAAC+GAAAhMgAAYAzrEWAM6xFtHQAAbR2lBW0dRgxtHUYM8e6lBfHuE+4OGOsRDhjrEb8RE+6/ERPuDhjrEQ4Y6xG/ERPuvxH//y+GE+4vhln6Dne48w53//8hMhPuITK48+1nWfrtZ1n6Y1C482NQWfpCQbjzQkHrEW0dAABtHQAAYAzrEWAM/////xPu//8T7vHu///x7g=="),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAWABUAFAAXABkAGgAUABkAFwAbABkAHAAaABkAGAAVABwAHQAZABsAHQAbABgAHAAZAB4AHwAdABgAGAAcAB8AHgAfABwAIAAhACIAIwAiACEAIgAkACAAIQAlACMAJQAmACMAIgAnACQAJwAmACgAJwAoACQAKQAoACYAJQApACYAKAAqACQAJQArACkAKgArACQAJQAkACsALAAtAC4ALgAvACwAMAAxADIAMgAzADAANAA1ADYANgA3ADQAOAA5ADoAOgA7ADgAPAA9AD4APgA/ADwAQABBAEIAQgBDAEAARABFAEYARgBHAEQASABJAEoASgBLAEgATABNAE4ATgBPAEwAUABRAFIAUgBTAFAAVABVAFYAVgBXAFQAWABWAFUAVQBZAFgAVgBaAFcAWABbAFYAWgBcAFcAWQBdAFgAWQBXAF0AWABeAFsAXABbAF4AXwBdAFcAXgBfAFwAXABfAFcAYABhAGIAYgBjAGAAZABlAGYAZgBnAGQAaABpAGoAagBrAGgAbABtAG4AbgBvAGwAcABxAHIAcgBzAHAAdAB1AHYAdwB2AHUAdgB4AHQAdQB5AHcAeQB6AHcAdgB7AHgAewB6AHwAewB8AHgAfQB8AHoAeQB9AHoAfAB+AHgAeQB/AH0AfgB/AHgAeQB4AH8AgACBAIIAggCDAIAAhACFAIYAhgCHAIQA"),
"material": SubResource("StandardMaterial3D_yu6lg"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(39.3701, 41.3701, 0, 0),
"vertex_count": 136,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_r0uwu")

[sub_resource type="CylinderShape3D" id="CylinderShape3D_ivckt"]
height = 0.3
radius = 46.0

[sub_resource type="SceneReplicationConfig" id="SceneReplicationConfig_e8heu"]
properties/0/path = NodePath(".:text")
properties/0/spawn = true
properties/0/replication_mode = 2

[node name="Game" type="Node"]
script = ExtResource("1_2isbr")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_54mir")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.887202, -0.423769, 0.182463, -0.0922675, 0.224523, 0.970091, -0.452062, -0.877502, 0.160097, 0, 0, 0)
light_energy = 2.0
light_specular = 2.5

[node name="Cloud" type="MeshInstance3D" parent="."]
transform = Transform3D(50, 0, 0, 0, 50, 0, 0, 0, 50, 3.39157, -53.3244, -0.590649)
material_override = SubResource("ShaderMaterial_2isbr")
cast_shadow = 0
mesh = SubResource("SphereMesh_ubh3g")
skeleton = NodePath("../..")

[node name="Level" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3.7, 31.4)

[node name="CSGCombiner3D3" type="CSGCombiner3D" parent="Level"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 0)
material_override = ExtResource("9_yu6lg")
use_collision = true
collision_layer = 5
collision_mask = 5

[node name="CSGPolygon3D8" type="CSGPolygon3D" parent="Level/CSGCombiner3D3"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -48.25, 8, -22)
use_collision = true
polygon = PackedVector2Array(0, 0, 3, 2, 3, 0)
depth = 4.0
material = ExtResource("9_yu6lg")

[node name="CSGBox3D8" type="CSGBox3D" parent="Level/CSGCombiner3D3"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -54.7, 5.10721, -20)
material_override = SubResource("StandardMaterial3D_80b28")
use_collision = true
size = Vector3(6.9, 9.78558, 10)

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Level/CSGCombiner3D3/CSGBox3D8"]
transform = Transform3D(1, 7.10543e-15, -4.37114e-08, -4.37114e-08, 1.64203e-15, -1, -7.10543e-15, 1, 3.55271e-15, -0.0460472, -3.81603, -0.719687)
operation = 2
radius = 2.0
height = 12.0938
sides = 16

[node name="CSGBox3D9" type="CSGBox3D" parent="Level/CSGCombiner3D3"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -47.0305, 4.05394, -20)
material_override = SubResource("StandardMaterial3D_61kcn")
use_collision = true
size = Vector3(7, 7.89212, 10)

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Level/CSGCombiner3D3/CSGBox3D9"]
transform = Transform3D(-4.37114e-08, 1, -4.37114e-08, 0, -4.37114e-08, -1, -1, -4.37114e-08, 1.91068e-15, -0.0460491, -2.21603, -1.06656)
operation = 2
radius = 2.52197
height = 8.1875
sides = 16

[node name="CSGCombiner3D" type="CSGCombiner3D" parent="Level"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.37738, 0, 0)
material_override = ExtResource("9_yu6lg")
use_collision = true
collision_layer = 5
collision_mask = 5

[node name="CSGBox3D5" type="CSGBox3D" parent="Level/CSGCombiner3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.20595, 2, -22)
use_collision = true
size = Vector3(5.96143, 4, 4)

[node name="CSGPolygon3D7" type="CSGPolygon3D" parent="Level/CSGCombiner3D"]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 1.22524, 4, -24)
use_collision = true
polygon = PackedVector2Array(0, 0, 3, 2, 3, 0)
depth = 4.0

[node name="CSGBox3D6" type="CSGBox3D" parent="Level/CSGCombiner3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.4049, 3.12769, -20)
use_collision = true
size = Vector3(7.25708, 5.74463, 8)

[node name="CSGBox3D7" type="CSGBox3D" parent="Level/CSGCombiner3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.0365, 4.05005, -20)
material_override = ExtResource("9_yu6lg")
use_collision = true
size = Vector3(6, 7.8999, 8)

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Level/CSGCombiner3D/CSGBox3D7"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, -0.200001, -2.34448, 0.120926)
operation = 2
radius = 2.3
height = 8.375
sides = 16

[node name="CSGPolygon3D9" type="CSGPolygon3D" parent="Level/CSGCombiner3D"]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -5.96755, 6, -22)
use_collision = true
polygon = PackedVector2Array(0, 0, 3.07048, 2.01077, 3, 0)
depth = 4.0

[node name="CSGCombiner3D2" type="CSGCombiner3D" parent="Level"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9.20697, 0.3, 0)
material_override = ExtResource("9_yu6lg")
use_collision = true
collision_layer = 4

[node name="CSGBox3D4" type="CSGBox3D" parent="Level/CSGCombiner3D2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 1, -29)
material_override = ExtResource("9_yu6lg")
use_collision = true
size = Vector3(4, 2, 4)

[node name="CSGPolygon3D6" type="CSGPolygon3D" parent="Level/CSGCombiner3D2"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 3, 0, -34)
use_collision = true
polygon = PackedVector2Array(0, 0, 3, 2, 3, 0)
depth = 4.0
material = ExtResource("9_yu6lg")

[node name="SpawnPoints" type="Node3D" parent="Level"]
script = ExtResource("12_r0uwu")

[node name="platform" type="CSGBox3D" parent="Level/SpawnPoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.9125, -0.25271, -31.3231)
visible = false
use_collision = true
size = Vector3(4.20923, 1, 4.89941)

[node name="platform2" type="CSGBox3D" parent="Level/SpawnPoints"]
transform = Transform3D(0.5, 0, -0.866025, 0, 1, 0, 0.866025, 0, 0.5, -28, -0.25271, -43.7262)
visible = false
use_collision = true
size = Vector3(4.33496, 1, 4.75879)

[node name="platform3" type="CSGBox3D" parent="Level/SpawnPoints"]
transform = Transform3D(-5.96046e-08, 0, -1, 0, 1, 0, 1, 0, -5.96046e-08, -14, -0.25271, -50.7262)
visible = false
use_collision = true
size = Vector3(4.1582, 1, 4.13379)

[node name="platform4" type="CSGBox3D" parent="Level/SpawnPoints"]
transform = Transform3D(-5.96046e-08, 0, -1, 0, 1, 0, 1, 0, -5.96046e-08, -34.3318, -0.25271, -29.2766)
visible = false
use_collision = true
size = Vector3(4.1582, 1, 4.13379)

[node name="platform5" type="CSGBox3D" parent="Level/SpawnPoints"]
transform = Transform3D(-5.96046e-08, 0, -1, 0, 1, 0, 1, 0, -5.96046e-08, -22.2027, -0.25271, -30.1499)
visible = false
use_collision = true
size = Vector3(4.1582, 1, 4.13379)

[node name="Beach" type="Node3D" parent="Level"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29.3547, -7.46678, -28.7778)

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Level/Beach"]
transform = Transform3D(1.53, 0, 0, 0, 1.53, 0, 0, 0, 1.53, 0, 20.1278, 0)
use_collision = true
collision_layer = 3
collision_mask = 3
radius = 26.0
height = 25.775
cone = true
material = SubResource("StandardMaterial3D_36okb")

[node name="CSGBox3D" type="CSGBox3D" parent="Level/Beach/CSGCylinder3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0499992, 2.41475, 0)
operation = 2
size = Vector3(69.9, 21, 59.4)
material = SubResource("StandardMaterial3D_yceoq")

[node name="Ocean" type="Node3D" parent="Level/Beach"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="Level/Beach/Ocean"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 5.9, 0)
mesh = SubResource("PlaneMesh_4h7cg")

[node name="CSGBox3D" type="CSGBox3D" parent="Level/Beach/Ocean"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 5.1, 0.549988)
material_override = SubResource("StandardMaterial3D_36okb")
use_collision = true
size = Vector3(799, 1, 801.5)

[node name="Trampoline" parent="Level/Beach" instance=ExtResource("14_nuc2m")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.52103, 7.79999, 0.4102)

[node name="Trampoline3" parent="Level/Beach" instance=ExtResource("14_nuc2m")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.80051, 7.79999, 18.3872)

[node name="Trampoline2" parent="Level/Beach" instance=ExtResource("14_nuc2m")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.02485, 7.79999, -13.326)

[node name="Objects" type="Node3D" parent="Level/Beach"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.399994, 0)

[node name="Node3D" type="Node3D" parent="Level/Beach/Objects"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1)

[node name="Root Scene" parent="Level/Beach/Objects/Node3D" instance=ExtResource("15_jg0gx")]
transform = Transform3D(5.79556, 0, -1.55291, 0, 6, 0, 1.55291, 0, 5.79556, 3.02951, 9.05793, 13.8988)

[node name="CSGBox3D" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, 1.7, 9.034, 17.143)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="CSGBox3D2" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, 2.05055, 9.034, 15.8347)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="CSGBox3D6" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, 2.47954, 9.034, 14.2337)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="CSGBox3D3" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, -0.924, 9.034, 13.32)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="CSGBox3D4" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, -1.35331, 9.034, 14.9222)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="CSGBox3D5" type="CSGBox3D" parent="Level/Beach/Objects/Node3D"]
transform = Transform3D(0.965926, 0, -0.258819, 0, 4.68, 0, 0.258819, 0, 0.965926, -1.70443, 9.034, 16.2326)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234, 0.705, 0.252)

[node name="Palm Tree8" parent="Level/Beach/Objects" instance=ExtResource("16_k2g4h")]
transform = Transform3D(4.24264, 0, 4.24264, 0, 6, 0, -4.24264, 0, 4.24264, -17.6397, 7.25538, 22.7619)

[node name="Palm Tree6" parent="Level/Beach/Objects" instance=ExtResource("16_k2g4h")]
transform = Transform3D(-5.19615, 0, -3, 0, 6, 0, 3, 0, -5.19615, 6.30958, 7.25538, -27.5664)

[node name="Palm Tree2" parent="Level/Beach/Objects" instance=ExtResource("17_j7amm")]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, 23.1963, 7.33392, 10.3324)

[node name="Palm Tree7" parent="Level/Beach/Objects" instance=ExtResource("17_j7amm")]
transform = Transform3D(5.4, 0, 0, 0, 5.4, 0, 0, 0, 5.4, 4.6049, 7.43748, 27.4731)
collision_layer = 1

[node name="Palm Tree4" parent="Level/Beach/Objects" instance=ExtResource("17_j7amm")]
transform = Transform3D(-2.4, 0, -4.15692, 0, 4.8, 0, 4.15692, 0, -2.4, -7.62078, 7.43748, -27.2978)

[node name="Palm Tree5" parent="Level/Beach/Objects" instance=ExtResource("17_j7amm")]
transform = Transform3D(2.82843, 0, -2.82843, 0, 4, 0, 2.82843, 0, 2.82843, -29.1007, 7.43748, -2.04683)

[node name="Palm Tree9" parent="Level/Beach/Objects" instance=ExtResource("16_k2g4h")]
transform = Transform3D(2.73178, 0, -5.34204, 0, 6, 0, 5.34204, 0, 2.73178, 21.8087, 7.25538, -19.9855)

[node name="Palm Tree10" parent="Level/Beach/Objects" instance=ExtResource("17_j7amm")]
transform = Transform3D(2.82843, 0, -2.82843, 0, 4, 0, 2.82843, 0, 2.82843, 10.3583, 7.43748, -5.16482)

[node name="Red_2FWhite Beach Umbrella" parent="Level/Beach/Objects" instance=ExtResource("18_hjokn")]
transform = Transform3D(8, 0, 0, 0, 8, 0, 0, 0, 8, -8.6747, 9.40744, 23.753)

[node name="Red_2FWhite Beach Umbrella2" parent="Level/Beach/Objects" instance=ExtResource("18_hjokn")]
transform = Transform3D(8, 0, 0, 0, 8, 0, 0, 0, 8, -5.6747, 9.40744, -21.047)

[node name="beach_umbrella_-_low_poly" parent="Level/Beach/Objects" instance=ExtResource("19_wmr3b")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.783869, 7.22635, 23.4001)

[node name="beach_umbrella_-_low_poly2" parent="Level/Beach/Objects" instance=ExtResource("19_wmr3b")]
transform = Transform3D(-1.2941, 0, -4.82963, 0, 5, 0, 4.82963, 0, -1.2941, 17.6652, 7.12635, -9.18542)

[node name="Dock" parent="Level/Beach/Objects" instance=ExtResource("20_43v55")]
transform = Transform3D(4.02766, 0, -6.91216, 0, 8, 0, 6.91216, 0, 4.02766, 13.5059, 10.0353, 20.6781)

[node name="Dock3" parent="Level/Beach/Objects" instance=ExtResource("20_43v55")]
transform = Transform3D(-2.54956, 0, -9.66953, 0, 10, 0, 9.66953, 0, -2.54956, 4.4979, 10.2283, -18.4572)

[node name="Crate2" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(7.07107, 0, -7.07107, 0, 10, 0, 7.07107, 0, 7.07107, -2.258, 7.37005, 3.18242)

[node name="Crate3" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(7.07107, 0, -7.07107, 0, 10, 0, 7.07107, 0, 7.07107, -1.05613, 7.37005, 4.38429)

[node name="Crate4" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(7.07107, 0, -7.07107, 0, 10, 0, 7.07107, 0, 7.07107, -1.01019, 9.02692, 4.43023)

[node name="Crate5" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(-1.47792, 0, -9.89019, 0, 10, 0, 9.89019, 0, -1.47792, -25.3465, 7.3561, -9.20694)

[node name="Crate6" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(8.30859, 0, -5.56484, 0, 10, 0, 5.56484, 0, 8.30859, -26.98, 7.3561, -10.4684)

[node name="Crate7" parent="Level/Beach/Objects" instance=ExtResource("21_7gt2u")]
transform = Transform3D(9.93515, 0, 1.13706, 0, 10, 0, -1.13706, 0, 9.93515, -26.8097, 7.3561, -7.32477)

[node name="Shipping Port2" parent="Level/Beach/Objects" instance=ExtResource("22_bc744")]
transform = Transform3D(4.99894, 0, 8.66086, 0, 10, 0, -8.66086, 0, 4.99894, -17.8803, 9.92841, -8.03976)
collision_layer = 1

[node name="Rock Large" parent="Level/Beach/Objects" instance=ExtResource("23_jggqy")]
transform = Transform3D(0.6, 0, 0, 0, 0.6, 0, 0, 0, 0.6, 0, 7.4, -4)

[node name="Rock Large3" parent="Level/Beach/Objects" instance=ExtResource("23_jggqy")]
transform = Transform3D(-0.263676, 0, -0.572167, 0, 0.6, 0, 0.572167, 0, -0.263676, -16.5695, 7.4, -23.2139)

[node name="Rock Large2" parent="Level/Beach/Objects" instance=ExtResource("23_jggqy")]
transform = Transform3D(0.6, 0, 0, 0, 0.6, 0, 0, 0, 0.6, -16.92, 7.4, 18.26)

[node name="Bush" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6.5, 7.30001, -8.5)

[node name="Bush7" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2.5, 7.25276, -21.7)

[node name="Bush2" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(1.93185, 0, -0.517638, 0, 2, 0, 0.517638, 0, 1.93185, 16.5682, 7.33248, -1.0886)

[node name="Bush3" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(1.93185, 0, -0.517638, 0, 2, 0, 0.517638, 0, 1.93185, -10.0897, 7.40001, 18.9238)

[node name="Bush5" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(1.93185, 0, -0.517638, 0, 2, 0, 0.517638, 0, 1.93185, -22.6984, 7.40001, 15.7524)

[node name="Bush6" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(0.174311, 0, -1.99239, 0, 2, 0, 1.99239, 0, 0.174311, -24.3178, 7.60001, 0.757785)

[node name="Bush8" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(0.174311, 0, -1.99239, 0, 2, 0, 1.99239, 0, 0.174311, -20.8811, 7.32914, -12.7821)

[node name="Bush4" parent="Level/Beach/Objects" instance=ExtResource("24_cjhsw")]
transform = Transform3D(1.28558, 0, -1.53209, 0, 2, 0, 1.53209, 0, 1.28558, -0.453179, 7.40001, 28.8524)

[node name="HighPlatform2" type="Node3D" parent="Level/Beach/Objects"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3.4931, 0, -1.02859)

[node name="CSGBox3D" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 5.59066, 8.67906, 11.235)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.252441)

[node name="CSGBox3D2" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 5.59066, 8.67906, 9.87923)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.273682)

[node name="CSGBox3D5" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 2.06431, 8.67906, 9.87923)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.273682)

[node name="CSGBox3D6" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 2.06431, 8.67906, 11.224)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.273682)

[node name="CSGBox3D7" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 2.06431, 8.67906, 8.2309)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.273682)

[node name="CSGBox3D3" type="CSGBox3D" parent="Level/Beach/Objects/HighPlatform2"]
transform = Transform3D(1, 0, 0, 0, 4.68, 0, 0, 0, 1, 5.59066, 8.67906, 8.23497)
material_override = SubResource("StandardMaterial3D_p2ijq")
use_collision = true
size = Vector3(0.234619, 2.67151, 0.252441)

[node name="Root Scene2" parent="Level/Beach/Objects/HighPlatform2" instance=ExtResource("15_jg0gx")]
transform = Transform3D(6, 0, 0, 0, 6, 0, 0, 0, 6, 6.03361, 11.5579, 7.75399)

[node name="fence_corner3" type="StaticBody3D" parent="Level/Beach/Objects"]
transform = Transform3D(-2.18557e-07, 0, 5, 0, 5, 0, -5, 0, -2.18557e-07, -22.045, 17.367, 6.347)

[node name="CollisionShape3D" type="CollisionShape3D" parent="Level/Beach/Objects/fence_corner3"]
transform = Transform3D(1, 0, 3.55271e-15, 0, 1, 0, -3.55271e-15, 0, 1, 0, -0.05, 0)
shape = SubResource("ConcavePolygonShape3D_ivckt")

[node name="tmpParent" type="Node3D" parent="Level/Beach/Objects/fence_corner3"]

[node name="fence_corner" type="MeshInstance3D" parent="Level/Beach/Objects/fence_corner3/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
mesh = SubResource("ArrayMesh_yu6lg")
skeleton = NodePath("")

[node name="fence_corner4" type="StaticBody3D" parent="Level/Beach/Objects"]
transform = Transform3D(-5, 0, -7.54979e-07, 0, 5, 0, 7.54979e-07, 0, -5, -22.045, 17.367, 10.998)

[node name="CollisionShape3D" type="CollisionShape3D" parent="Level/Beach/Objects/fence_corner4"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
shape = SubResource("ConcavePolygonShape3D_6h7qy")

[node name="tmpParent" type="Node3D" parent="Level/Beach/Objects/fence_corner4"]

[node name="fence_corner" type="MeshInstance3D" parent="Level/Beach/Objects/fence_corner4/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
mesh = SubResource("ArrayMesh_hqmqs")
skeleton = NodePath("")

[node name="Powerup_Spwanpoints" type="Node3D" parent="Level"]
unique_name_in_owner = true

[node name="Marker3D" type="Marker3D" parent="Level/Powerup_Spwanpoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17.5347, 0.825219, -46.8801)

[node name="Marker3D3" type="Marker3D" parent="Level/Powerup_Spwanpoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21.2809, 0.825219, -38.0355)

[node name="Marker3D4" type="Marker3D" parent="Level/Powerup_Spwanpoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.0107, 0.825219, -39.1976)

[node name="Marker3D5" type="Marker3D" parent="Level/Powerup_Spwanpoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.4224, 0.825219, -32.7099)

[node name="Glue_Spawnpoints" type="Node3D" parent="Level"]

[node name="glue" type="Marker3D" parent="Level/Glue_Spawnpoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2.49238, 0.5, -37.294)

[node name="Powerup" parent="Level/Glue_Spawnpoints" instance=ExtResource("24_2isbr")]
transform = Transform3D(0.965926, 0, 0.258819, 0, 1, 0, -0.258819, 0, 0.965926, -6, 0.298006, -39)

[node name="Area3D" type="Area3D" parent="Level"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
collision_mask = 3

[node name="CollisionShape3D" type="CollisionShape3D" parent="Level/Area3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29.1, -1.55, -27.5)
shape = SubResource("CylinderShape3D_ivckt")
debug_color = Color(0.574023, 0, 0.701961, 1)

[node name="PlayerSpawner" type="MultiplayerSpawner" parent="." node_paths=PackedStringArray("spawn_points")]
unique_name_in_owner = true
_spawnable_scenes = PackedStringArray("uid://cm26nto6fstcx")
spawn_path = NodePath("../Players")
script = ExtResource("25_tutp8")
player_scene = ExtResource("26_5epq8")
spawn_points = NodePath("../Level/SpawnPoints")

[node name="Players" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="Label3D" type="Label3D" parent="."]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -12.2012, 7.00993, 7.37873)
visible = false
pixel_size = 0.01
text = "0"
font_size = 128
outline_size = 32
script = ExtResource("27_hfixu")

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="Label3D"]
replication_config = SubResource("SceneReplicationConfig_e8heu")

[node name="CounterTimer" type="Timer" parent="Label3D"]
unique_name_in_owner = true
autostart = true

[node name="PlayerCount" type="Label3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -3.746, 3.74138, 7.37873)
visible = false
pixel_size = 0.01
text = "players : "
font_size = 128
outline_size = 32

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="PlayerCount"]
replication_config = SubResource("SceneReplicationConfig_e8heu")

[node name="Explosion" parent="." instance=ExtResource("28_rvdiq")]
unique_name_in_owner = true

[node name="UI" type="CanvasLayer" parent="."]

[node name="hot_potato_timer_label" type="Label" parent="UI"]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 576.0
offset_top = 128.0
offset_right = 867.0
offset_bottom = 195.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 48
text = "00:00"
horizontal_alignment = 2

[node name="BombTimerLabel" type="Label" parent="UI"]
unique_name_in_owner = true
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -124.0
offset_top = 40.0
offset_right = -68.0
offset_bottom = 85.0
grow_horizontal = 0
theme_override_font_sizes/font_size = 32
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BombTimerLabel3" type="Label" parent="UI"]
unique_name_in_owner = true
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -472.0
offset_top = 40.0
offset_right = -135.0
offset_bottom = 85.0
grow_horizontal = 0
theme_override_font_sizes/font_size = 32
text = "Bomb Explode Timer :"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LobbyTimerLabel" type="Label" parent="UI"]
unique_name_in_owner = true
visible = false
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -144.0
offset_top = -304.0
offset_right = 128.0
offset_bottom = -117.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.917969, 0.406786, 0.125996, 1)
theme_override_constants/outline_size = 24
theme_override_fonts/font = ExtResource("27_54mir")
theme_override_font_sizes/font_size = 164
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Players_left_container" type="GridContainer" parent="UI"]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -32.0
offset_top = 24.0
offset_right = 32.0
offset_bottom = 120.0
grow_horizontal = 2
columns = 10

[node name="Virtual Joystick" parent="UI" instance=ExtResource("29_cnxcx")]
visible = false
anchors_preset = -1
offset_left = 64.0
offset_top = -672.0
offset_right = 490.0
offset_bottom = -182.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(1.2, 1.2)
joystick_mode = 1
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[node name="jump" type="Control" parent="UI"]
visible = false
layout_mode = 3
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -317.0
offset_top = -320.0
offset_right = -131.0
offset_bottom = -135.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(1.2, 1.2)
script = ExtResource("30_7tpd1")
metadata/_edit_use_anchors_ = true

[node name="TouchScreenButton" type="TouchScreenButton" parent="UI/jump"]
position = Vector2(0, 6.66669)
scale = Vector2(0.353282, 0.353282)
texture_normal = ExtResource("31_nrlga")
action = "jump"

[node name="Countdown" type="AudioStreamPlayer" parent="."]
unique_name_in_owner = true
stream = ExtResource("31_2isbr")

[node name="CountdownEnd" type="AudioStreamPlayer" parent="."]
unique_name_in_owner = true
stream = ExtResource("32_mb8xf")

[node name="game_over" type="AudioStreamPlayer" parent="."]
unique_name_in_owner = true
stream = ExtResource("33_ivckt")

[node name="Player" parent="." instance=ExtResource("26_5epq8")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 4.4, 0)

[node name="PowerupSpeed" parent="." instance=ExtResource("30_2isbr")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9.26967, 5.08989, -12.0129)

[node name="PowerupSpeed2" parent="." instance=ExtResource("30_2isbr")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16.2697, 5.08989, 0.987101)

[node name="PowerUpTime" parent="." instance=ExtResource("31_ubh3g")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.27561, 4.9708, -3.67565)

[node name="PowerUpTime2" parent="." instance=ExtResource("31_ubh3g")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24.4756, 8.4708, -15.4757)

[connection signal="body_entered" from="Level/Area3D" to="." method="_on_area_3d_body_entered"]
[connection signal="timeout" from="Label3D/CounterTimer" to="Label3D" method="_on_counter_timer_timeout"]
[connection signal="pressed" from="UI/jump/TouchScreenButton" to="UI/jump" method="_on_touch_screen_button_pressed"]
[connection signal="released" from="UI/jump/TouchScreenButton" to="UI/jump" method="_on_touch_screen_button_released"]
