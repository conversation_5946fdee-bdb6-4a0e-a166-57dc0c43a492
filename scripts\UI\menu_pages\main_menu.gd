extends Node3D

@onready var user_name: Label = $"GUI/Menu/Profile name/Label"
@onready var camera_3d: Camera3D = %Camera3D
@onready var count_down_timer: Timer = %CountDownTimer

@onready var coin: Label = $GUI/Menu/Coin/Label
@onready var cup: Label = $GUI/Menu/Cup/Label
var set_coin = false
var set_cup = false

# --- MODIFICATION START: Variables for movement and initial state ---

# Movement targets
var target_position: Vector3
var target_camera_rotation_x: float
var target_platform_rotation_y: float

# Store the initial state
var initial_camera_position: Vector3
var initial_camera_rotation: Vector3
var initial_platform_rotation: Vector3

# Movement properties
var is_moving: bool = false
var speed: float = 4.0
var rotation_speed: float = 4.0
@onready var platform: StaticBody3D = $"block-grass-overhang-large"

# --- MODIFICATION END ---

@onready var menu_page: Control = %Menu
@onready var search_page: Control = %Search
@onready var player_join_container: GridContainer = %PlayerJoinContainer
const playerjoincard := preload("res://scenes/Utils/PlayerJoinCard.tscn")

@onready var wait_time: Label = %wait_time
@onready var players_count_label: Label = $GUI/Search/players_count
@onready var unseen_messages: Button = $GUI/Menu/Friends/unseen_messages
@onready var game_menu_panel: Panel = $GUI/GamesMenu/GameMenuPanel



var remaining_wait_time: int = 0 # Stores the current countdown value
var players_count := 0

var plugin
var database : SQLite

signal connect_client

var base_skin : Array = [1, 1, 0, 5, 0, 0, 0, 5, 0]


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	print("SECOND: " + str(Global.skin_parameters))
	var mesh_child = %Model.get_node("Skeleton_01/Skeleton3D/" + "shirts")
	if mesh_child:
		# Clear the mesh by setting it to null
		mesh_child.mesh = null
		# Set the skin parameter to 0 (cleared)
		#skin_parameter_holder[mesh_child.get_index()] = 0
		#print("Cleared skin for category: " + category_name)
	else:
		pass
		#push_error("Node not found for category: " + category_name)
	await get_tree().create_timer(2.0).timeout
	if Global.skin_parameters != base_skin:
		set_player_skin()
	user_name.text = Global.global_player_name
	coin.text = Global.player_coin
	cup.text = Global.player_cup
	Tcpserver.connect("gnsearch_recieved", Callable(self, "_on_gnsearch_recieved"))
	Tcpserver.connect("gamestart_recieved", Callable(self, "_on_gamestart_recieved"))
	Tcpserver.connect("frchat_received", Callable(self, "_on_frchat_received"))
	Tcpserver.send_request("gud")
	
	database = SQLite.new()
	database.path = "user://chat.db"
	database.open_db()

	initial_camera_position = camera_3d.position
	initial_camera_rotation = camera_3d.rotation_degrees
	initial_platform_rotation = platform.rotation_degrees
	_on_frchat_received({})

func set_player_skin():
	var skin_meshes := %Model.get_node("Skeleton_01/Skeleton3D/").get_children()

	# Remove last 2 skins if unwanted
	skin_meshes = skin_meshes.slice(0, skin_meshes.size() - 2)

	for i in range(min(skin_meshes.size(), Global.skin_parameters.size())):
		var skin = skin_meshes[i]
		var param_index = Global.skin_parameters[i]

		var mesh_child = %Model.get_node("Skeleton_01/Skeleton3D/" + str(skin.name))

		if param_index == 0:
			# Clear mesh if index is 0
			mesh_child.mesh = null
			#print("Cleared mesh for: ", skin.name)
		else:
			# Build path to file
			var resource_path = "user://scenes/NewPlayer/resources/".path_join(str(skin.name))
			var file_path = resource_path.path_join(str(param_index) + ".tres")

			var resource = load(file_path)
			if resource is Mesh:
				mesh_child.mesh = resource
				#print("Applied: ", file_path)
			else:
				push_warning("Could not load mesh: %s" % file_path)

func _notification(what):
	match what:
		NOTIFICATION_APPLICATION_PAUSED:
			print("App paused (home/switch)")
			if plugin:
				plugin.gameExited()
		
		NOTIFICATION_WM_GO_BACK_REQUEST :
			print("Quit requested (back button)")
			# --- MODIFICATION START: Handle back press to return to menu view ---
			# If the search page is visible, go back to the menu instead of quitting.
			if search_page.visible:
				_on_back_pressed()
			else:
				get_tree().quit() # Or use plugin.gameExited() if needed
			# --- MODIFICATION END ---
		
		NOTIFICATION_APPLICATION_RESUMED:
			print("App resumed")
			if plugin:
				plugin.gameEntered()

func _create_chats_table():
	var table = {
		"id": { "data_type": "INTEGER", "primary_key": true, "not_null": true, "auto_increment": true },
		"name": {"data_type": "TEXT"},
		"hid": {"data_type": "INTEGER"},
		"ax": {"data_type": "TEXT"},
		"read": {"data_type": "INTEGER"},
		"date": {"data_type": "TEXT"},
		"me": {"data_type": "INTEGER"},
		"message": {"data_type": "TEXT"},
		"msgrepid": {"data_type": "TEXT"}
	}
	database.create_table("chats", table)


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	set_coin_and_cup()
	if is_moving:
		# Smoothly move camera to the target position
		camera_3d.position = camera_3d.position.lerp(target_position, speed * delta)
		
		# Smoothly rotate camera on its X-axis
		var current_cam_rot_x = camera_3d.rotation_degrees.x
		camera_3d.rotation_degrees.x = lerp(current_cam_rot_x, target_camera_rotation_x, rotation_speed * delta)

		# Smoothly rotate platform on its Y-axis
		var current_platform_rot_y = platform.rotation_degrees.y
		platform.rotation_degrees.y = lerp(current_platform_rot_y, target_platform_rotation_y, rotation_speed * delta)
		
		# Check if movement is complete
		if camera_3d.position.distance_to(target_position) < 0.01 and \
			abs(camera_3d.rotation_degrees.x - target_camera_rotation_x) < 0.1 and \
			abs(platform.rotation_degrees.y - target_platform_rotation_y) < 0.1:
			
			# Snap to final positions to ensure accuracy
			camera_3d.position = target_position
			camera_3d.rotation_degrees.x = target_camera_rotation_x
			platform.rotation_degrees.y = target_platform_rotation_y
			is_moving = false


func set_coin_and_cup():
	if (Global.player_coin != "0" and !set_coin):
		coin.text = Global.player_coin
		set_coin = true
	elif (Global.player_cup != "0" and !set_cup):
		cup.text = Global.player_cup
		set_cup = true


func _on_start_pressed() -> void:
	LoadScene.change_level("res://scenes/glass_game/glass_game.tscn")


func _on_settings_pressed() -> void:
	get_tree().change_scene_to_file("res://scenes/UI/settings.tscn")


func _on_exit_pressed() -> void:
	get_tree().quit()


func _on_city_pressed() -> void:
	pass
	#LoadScene.change_level("res://scenes/TestLevels/waiting_room.tscn")
	#get_tree().change_scene_to_file("res://scenes/TestLevels/waiting_room.tscn")
	%BrowseServer.modulate.a = 0.0
	%BrowseServer.show()
	var tween = create_tween()
	tween.tween_property(%BrowseServer, "modulate:a", 1.0, 0.2)
	


func _on_profile_name_pressed() -> void:
	Tcpserver.send_request("gudp")
	get_tree().change_scene_to_file("res://scenes/UI/Profile/Profile.tscn")
	#change_scene("res://scenes/UI/Profile/Profile.tscn")


func _on_friends_pressed() -> void:
	change_scene("res://scenes/UI/Friends/friends_no_background.tscn")


func _on_ranking_pressed() -> void:
	change_scene("res://scenes/UI/Ranking/ranking_no_background.tscn")
	


func _on_chat_pressed() -> void:
	change_scene("res://scenes/UI/Friends/chat_private.tscn")


func _on_shop_pressed() -> void:
	Tcpserver.send_request("gshopd")


func change_scene(path: String) -> void:
	call_deferred("_do_scene_change", path)

func _do_scene_change(path: String) -> void:
	get_tree().change_scene_to_file(path)
	
func _on_frchat_received(data: Dictionary) -> void:
	#database = SQLite.new()
	#database.path = "user://chat.db"
	#database.open_db()
	database.query("SELECT name FROM sqlite_master WHERE type='table' AND name='chats';")
	if  database.query_result.size() != 0:
		create_chats_table()
		database.query("SELECT COUNT(*) AS cnt FROM chats WHERE read = 0;")
		print("QUERY RESULT:", database.query_result)
		if database.query_result.size() > 0:
			var unread_count = database.query_result[0].get("cnt", 0)
			print("Unread rows:", unread_count)
			if unread_count > 0:
				unseen_messages.text = str(unread_count)
				unseen_messages.show()
			else:
				unseen_messages.text = "0"
				unseen_messages.hide()
				
		else:
			print("No result returned from COUNT query.")

func create_chats_table():
	var table = {
		"id": {"data_type": "int", "primary_key": true, "not_null": true, "auto_increment": true},
		"name": {"data_type": "text"},
		"message": {"data_type": "text"},
		"hid": {"data_type": "int"},
		"ax": {"data_type": "text"},
		"read": {"data_type": "int"},
		"date": {"data_type": "real"}, # store Unix timestamps (float)
		"me": {"data_type": "int"}     # store bool as int: 0 or 1
	}
	database.create_table("chats", table)

func _on_gnsearch_recieved(data: Dictionary) -> void:
	# 1. STOP the timer, get the new time, and RESET the countdown
	count_down_timer.stop()
	players_count = 0
	if data.has("time"):
		remaining_wait_time = data.get("time", "0").to_int()
		wait_time.text = str(remaining_wait_time)
		if remaining_wait_time > 0:
			count_down_timer.start()
			
	for child in player_join_container.get_children():
		child.queue_free()

	var users: Array = data.get("ulist", [])
	for user_data_string in users:
		if not typeof(user_data_string) == TYPE_STRING:
			continue
		var fields: PackedStringArray = user_data_string.split("|", false)
		if fields.size() < 3:
			continue

		var user_name_str: String = fields[1]
		var profile_picture_name: String = fields[2]
		var card = playerjoincard.instantiate()
		player_join_container.add_child(card)
		card.user_name.text = user_name_str
		
		# Image loading logic...
		var image_path = "res://assets/profiles/" + profile_picture_name
		if profile_picture_name != "0" and ResourceLoader.exists(image_path):
			card.profile.texture = load(image_path)
		else:
			card.profile.texture = load("res://assets/images/profile_placeHolder.png")
			
		
		players_count += 1
	players_count_label.text = str(players_count)
			

func _on_gamestart_recieved(data:Dictionary) -> void:
	
	if data.has("port"):
		var server_port = data.get("port", "0")
		Global.port = server_port
		print("SERVER PORT IS : " + str(Global.port))
	if data.has("num"):
		var number_of_players = data.get("num", "0")
		Global.number_of_players = number_of_players
		print("GLOBAL NUMBER OF PLAYERS ARE : " + str(Global.number_of_players))
	await get_tree().create_timer(3.0).timeout
	Global.server = false
	get_tree().change_scene_to_file("res://scenes/bomb_tag/bomb_game.tscn")

func _on_count_down_timer_timeout() -> void:
	# This function is called every 1 second by the Timer node
	if remaining_wait_time > 0:
		remaining_wait_time -= 1
		wait_time.text = str(remaining_wait_time)
	else:
		# Stop the timer if it reaches zero to save resources
		count_down_timer.stop()


func _on_games_pressed() -> void:
	%GamesMenu.modulate.a = 0.0
	%GamesMenu.show()
	var tween = create_tween()
	tween.tween_property(%GamesMenu, "modulate:a", 1.0, 0.2)


func _on_back_pressed() -> void:
	search_page.hide()
	%GamesMenu.hide()
	%BrowseServer.hide()
	menu_page.show()
	# --- MODIFICATION START: Set targets to the stored initial state ---
	target_position = initial_camera_position
	target_camera_rotation_x = initial_camera_rotation.x
	target_platform_rotation_y = initial_platform_rotation.y
	is_moving = true


func _on_bomb_tag_pressed() -> void:
	search_page.show()
	menu_page.hide()
	var tween = create_tween()
	tween.tween_property(%GamesMenu, "modulate:a", 0.0, 0.2)
	tween.tween_callback(%GamesMenu.hide)
	target_position = Vector3(-15, 10.9, 18)
	target_camera_rotation_x = -11.8
	target_platform_rotation_y = 0 # Or any other rotation you want for this view
	is_moving = true
	Tcpserver.send_gnsearch(1)


func _on_game_menu_back_pressed() -> void:
	var tween = create_tween()
	tween.tween_property(%GamesMenu, "modulate:a", 0.0, 0.2)
	tween.tween_callback(%GamesMenu.hide)



func _on_browse_menu_back_pressed() -> void:
	var tween = create_tween()
	tween.tween_property(%BrowseServer, "modulate:a", 0.0, 0.2)
	tween.tween_callback(%BrowseServer.hide)
